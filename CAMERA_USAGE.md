# Camera使用指南

本指南介绍如何在BeeSwarm系统中直接查看和使用本地camera，无需复杂的MQTT或ROS配置。

## 系统信息

**检测到的Camera设备:**
- Camera 0: /dev/video0 (1280x720 @ 10fps)
- Camera 2: /dev/video2 (1280x720 @ 10fps) 
- Camera 4: /dev/video4 (1280x720 @ 10fps)
- Camera 6: /dev/video6 (1280x720 @ 10fps)

**注意:** video1, video3, video5, video7 是元数据设备，不用于图像捕获。

## 可用工具

### 1. 完整功能Demo (`camera_demo.py`)

这是一个功能完整的camera管理工具，支持检测、拍照、批量拍摄和实时查看。

#### 基本用法:
```bash
# 检测可用camera
python3 camera_demo.py --detect

# 实时查看camera (按q退出，s保存，c连续保存)
python3 camera_demo.py --live 0

# 拍摄单张照片
python3 camera_demo.py --photo 0

# 批量拍摄 (camera_id, 数量)
python3 camera_demo.py --batch 0 5

# 测试所有camera
python3 camera_demo.py --test-all

# 交互式菜单 (默认)
python3 camera_demo.py
```

#### 实时查看控制:
- **q**: 退出
- **s**: 保存当前帧
- **c**: 切换连续保存模式 (每秒保存一张)

### 2. 简单实时查看器 (`simple_camera_viewer.py`)

轻量级的实时camera查看工具。

```bash
# 查看指定camera
python3 simple_camera_viewer.py 0
python3 simple_camera_viewer.py 2
python3 simple_camera_viewer.py 4
python3 simple_camera_viewer.py 6
```

#### 控制:
- **q**: 退出
- **s**: 保存当前帧

### 3. 批量拍摄工具 (`batch_capture.py`)

专门用于批量拍摄和延时摄影。

```bash
# 拍摄所有camera
python3 batch_capture.py --all

# 拍摄指定camera
python3 batch_capture.py --camera 0

# 延时拍摄 (camera_id, 张数, 间隔秒数)
python3 batch_capture.py --timelapse 0 10 2

# 指定保存目录和文件前缀
python3 batch_capture.py --all --dir my_photos --prefix "test"
```

## 使用示例

### 快速测试所有camera
```bash
cd /home/<USER>/BeeSwarm/RobotsManagement/agent_management
python3 /home/<USER>/BeeSwarm/camera_demo.py --test-all
```

### 实时查看camera 0
```bash
python3 /home/<USER>/BeeSwarm/simple_camera_viewer.py 0
```

### 批量拍摄所有camera
```bash
python3 /home/<USER>/BeeSwarm/batch_capture.py --all --dir captures_$(date +%Y%m%d_%H%M%S)
```

### 延时摄影 (camera 0, 20张照片, 每5秒一张)
```bash
python3 /home/<USER>/BeeSwarm/batch_capture.py --timelapse 0 20 5 --dir timelapse_$(date +%Y%m%d_%H%M%S)
```

## 文件保存

### 默认保存位置:
- `camera_demo.py`: `camera_captures/`
- `simple_camera_viewer.py`: 当前目录
- `batch_capture.py`: `captures/` (可自定义)

### 文件命名格式:
- 单张照片: `camera_{id}_{timestamp}.jpg`
- 批量照片: `{prefix}_camera_{id}_{timestamp}.jpg`
- 延时照片: `timelapse_camera_{id}_{序号}_{timestamp}.jpg`

## 故障排除

### 1. Camera被占用
如果看到 "Device is busy" 错误，可能是ROS或其他程序在使用camera:
```bash
# 停止ROS节点
pkill -f usb_cam
pkill -f roscore

# 或重启camera硬件
sudo rmmod uvcvideo
sudo modprobe uvcvideo
```

### 2. 权限问题
确保用户在video组中:
```bash
sudo usermod -a -G video $USER
# 然后重新登录
```

### 3. 检查camera状态
```bash
# 查看camera设备
ls -l /dev/video*

# 查看camera信息
v4l2-ctl --list-devices

# 测试camera
v4l2-ctl --device=/dev/video0 --all
```

## 性能优化

### 1. 减少延迟
- 使用较低的分辨率 (640x480)
- 减少缓冲帧数
- 使用MJPEG格式

### 2. 批量操作
- 使用批量拍摄工具而不是循环调用单张拍摄
- 在camera之间添加短暂延迟避免冲突

### 3. 存储优化
- 定期清理旧照片
- 使用压缩格式 (JPEG质量90-95)
- 考虑使用外部存储设备

## 集成到BeeSwarm系统

这些工具可以轻松集成到现有的BeeSwarm系统中:

1. **作为独立模块**: 直接调用脚本进行camera操作
2. **集成到web界面**: 通过Flask路由调用camera功能
3. **ROS节点包装**: 将功能包装为ROS服务
4. **MQTT发布**: 将拍摄的照片通过MQTT发布

## 下一步

1. 根据需要修改分辨率和帧率设置
2. 添加图像处理功能 (滤镜、标注等)
3. 实现自动拍摄调度
4. 添加云存储上传功能
5. 集成到现有的机器人控制系统

---

**提示**: 所有脚本都包含详细的帮助信息，使用 `--help` 参数查看完整选项。
