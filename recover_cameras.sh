#!/bin/bash
# 摄像头设备恢复脚本

echo "🔧 摄像头设备恢复脚本"
echo "========================"

echo "1. 检查当前USB设备..."
echo "USB设备列表:"
lsusb
echo ""

echo "2. 查找摄像头设备..."
CAMERA_DEVICES=$(lsusb | grep -i -E "(camera|video|webcam|uvc)")
if [ -n "$CAMERA_DEVICES" ]; then
    echo "✅ 找到摄像头设备:"
    echo "$CAMERA_DEVICES"
else
    echo "❌ 未找到摄像头设备"
fi
echo ""

echo "3. 检查内核模块..."
echo "UVC模块状态:"
lsmod | grep uvc || echo "❌ UVC模块未加载"
echo "Video模块状态:"
lsmod | grep video || echo "❌ Video模块未加载"
echo ""

echo "4. 检查系统日志..."
echo "最近的USB日志:"
dmesg | tail -20 | grep -i usb || echo "无USB相关日志"
echo ""

echo "5. 卸载摄像头驱动..."
sudo rmmod uvcvideo 2>/dev/null && echo "✅ 卸载uvcvideo" || echo "⚠️ uvcvideo未加载"
sudo rmmod videobuf2_vmalloc 2>/dev/null && echo "✅ 卸载videobuf2_vmalloc" || echo "⚠️ videobuf2_vmalloc未加载"
sudo rmmod videobuf2_memops 2>/dev/null && echo "✅ 卸载videobuf2_memops" || echo "⚠️ videobuf2_memops未加载"
sudo rmmod videobuf2_v4l2 2>/dev/null && echo "✅ 卸载videobuf2_v4l2" || echo "⚠️ videobuf2_v4l2未加载"
sudo rmmod videobuf2_common 2>/dev/null && echo "✅ 卸载videobuf2_common" || echo "⚠️ videobuf2_common未加载"
sudo rmmod videodev 2>/dev/null && echo "✅ 卸载videodev" || echo "⚠️ videodev未加载"

echo "6. 等待系统稳定..."
sleep 3

echo "7. 重新加载驱动..."
sudo modprobe videodev && echo "✅ 加载videodev" || echo "❌ 加载videodev失败"
sudo modprobe videobuf2_common && echo "✅ 加载videobuf2_common" || echo "❌ 加载videobuf2_common失败"
sudo modprobe videobuf2_v4l2 && echo "✅ 加载videobuf2_v4l2" || echo "❌ 加载videobuf2_v4l2失败"
sudo modprobe videobuf2_memops && echo "✅ 加载videobuf2_memops" || echo "❌ 加载videobuf2_memops失败"
sudo modprobe videobuf2_vmalloc && echo "✅ 加载videobuf2_vmalloc" || echo "❌ 加载videobuf2_vmalloc失败"
sudo modprobe uvcvideo && echo "✅ 加载uvcvideo" || echo "❌ 加载uvcvideo失败"

echo "8. 等待设备初始化..."
sleep 5

echo "9. 重启USB子系统..."
echo "重启USB端口..."
for usb_port in /sys/bus/usb/devices/usb*/authorized; do
    if [ -f "$usb_port" ]; then
        echo 0 | sudo tee "$usb_port" > /dev/null
    fi
done
sleep 2
for usb_port in /sys/bus/usb/devices/usb*/authorized; do
    if [ -f "$usb_port" ]; then
        echo 1 | sudo tee "$usb_port" > /dev/null
    fi
done

echo "10. 等待USB重新初始化..."
sleep 5

echo "11. 检查恢复结果..."
echo "Video设备:"
if ls /dev/video* 2>/dev/null; then
    echo "✅ 找到video设备"
    echo "设备详情:"
    ls -la /dev/video*
else
    echo "❌ 仍未找到video设备"
fi

echo ""
echo "USB摄像头设备:"
CAMERA_DEVICES_AFTER=$(lsusb | grep -i -E "(camera|video|webcam|uvc)")
if [ -n "$CAMERA_DEVICES_AFTER" ]; then
    echo "✅ USB摄像头设备:"
    echo "$CAMERA_DEVICES_AFTER"
else
    echo "❌ 仍未找到USB摄像头设备"
fi

echo ""
echo "12. 测试摄像头访问..."
python3 << 'EOF'
import cv2
import sys

print("测试摄像头访问...")
cameras = [0, 1, 2, 3, 4, 5, 6, 7]  # 扩大搜索范围
working_cameras = []

for cam_id in cameras:
    try:
        cap = cv2.VideoCapture(cam_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"✅ Camera {cam_id}: 工作正常")
                working_cameras.append(cam_id)
            else:
                print(f"⚠️ Camera {cam_id}: 可打开但无法读取")
        cap.release()
    except Exception as e:
        pass  # 静默处理错误

if working_cameras:
    print(f"\n🎉 找到 {len(working_cameras)} 个工作的摄像头: {working_cameras}")
else:
    print(f"\n❌ 没有找到工作的摄像头")
EOF

echo ""
echo "========================"
echo "🏁 恢复脚本完成"

# 给出建议
if ls /dev/video* 2>/dev/null >/dev/null; then
    echo "✅ 摄像头设备已恢复"
else
    echo "❌ 摄像头设备未恢复，建议:"
    echo "   1. 检查硬件连接"
    echo "   2. 重启系统: sudo reboot"
    echo "   3. 检查摄像头是否损坏"
fi
