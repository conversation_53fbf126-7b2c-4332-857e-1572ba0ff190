#!/usr/bin/env python3
"""
棋盘格检测调试工具
帮助排查为什么检测不到棋盘格
"""

import cv2
import numpy as np
import argparse

class ChessboardDebugger:
    def __init__(self):
        # 可能的棋盘格尺寸
        self.possible_sizes = [
            (8, 11),  # 用户的棋盘格
            (11, 8),  # 旋转90度
            (9, 6),   # 常见尺寸
            (8, 6),
            (7, 5),
            (6, 4),
            (9, 7),
            (8, 5),
            (10, 7),
            (6, 9),
            (5, 7),
        ]
    
    def debug_detection(self, camera_id):
        """调试棋盘格检测"""
        print("🔍 棋盘格检测调试工具")
        print("操作说明:")
        print("  - 将棋盘格放在摄像头前")
        print("  - 程序会尝试所有可能的尺寸")
        print("  - 按 'q' 退出")
        print("  - 按 's' 保存当前检测结果")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"❌ 无法打开摄像头 {camera_id}")
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            display_frame = frame.copy()
            
            # 尝试检测所有可能的尺寸
            detected_sizes = []
            best_corners = None
            best_size = None
            
            for board_size in self.possible_sizes:
                ret_corners, corners = cv2.findChessboardCorners(gray, board_size, None)
                
                if ret_corners:
                    # 精确化角点
                    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                    corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                    
                    detected_sizes.append(board_size)
                    if best_corners is None:  # 使用第一个检测到的
                        best_corners = corners_refined
                        best_size = board_size
            
            # 显示检测结果
            if best_corners is not None:
                # 绘制角点
                cv2.drawChessboardCorners(display_frame, best_size, best_corners, True)
                
                # 显示成功信息
                cv2.putText(display_frame, f"DETECTED: {best_size[0]}x{best_size[1]}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                cv2.putText(display_frame, f"Grid: {best_size[0]+1}x{best_size[1]+1} squares", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                # 显示所有检测到的尺寸
                if len(detected_sizes) > 1:
                    sizes_text = "Also detected: " + ", ".join([f"{s[0]}x{s[1]}" for s in detected_sizes[1:]])
                    cv2.putText(display_frame, sizes_text, (10, 110),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # 绿色边框
                cv2.rectangle(display_frame, (5, 5), (display_frame.shape[1]-5, display_frame.shape[0]-5), 
                             (0, 255, 0), 5)
                
                print(f"✅ Frame {frame_count}: 检测到 {len(detected_sizes)} 种尺寸，主要: {best_size[0]}x{best_size[1]}")
                
            else:
                # 显示未检测到
                cv2.putText(display_frame, "NO CHESSBOARD DETECTED", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
                cv2.putText(display_frame, "Try adjusting:", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                cv2.putText(display_frame, "- Position/angle", (10, 100),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                cv2.putText(display_frame, "- Lighting", (10, 130),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                cv2.putText(display_frame, "- Distance", (10, 160),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                
                # 红色边框
                cv2.rectangle(display_frame, (5, 5), (display_frame.shape[1]-5, display_frame.shape[0]-5), 
                             (0, 0, 255), 5)
            
            # 显示尝试的尺寸
            cv2.putText(display_frame, f"Trying sizes: {len(self.possible_sizes)} variants", 
                       (10, display_frame.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(display_frame, "Press 'q' to quit, 's' to save", 
                       (10, display_frame.shape[0] - 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            cv2.imshow('Chessboard Detection Debug', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存调试图像
                import time
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"debug_chessboard_{timestamp}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"💾 保存调试图像: {filename}")
                
                if best_size:
                    print(f"📋 检测结果: {best_size[0]}x{best_size[1]} 内角点")
                    print(f"📋 对应方格: {best_size[0]+1}x{best_size[1]+1}")
        
        cap.release()
        cv2.destroyAllWindows()
    
    def test_image_file(self, image_path):
        """测试单张图像的检测"""
        print(f"🔍 测试图像文件: {image_path}")
        
        img = cv2.imread(image_path)
        if img is None:
            print("❌ 无法读取图像文件")
            return
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        print("尝试检测不同尺寸的棋盘格...")
        
        for board_size in self.possible_sizes:
            ret, corners = cv2.findChessboardCorners(gray, board_size, None)
            
            if ret:
                print(f"✅ 检测成功: {board_size[0]}x{board_size[1]} 内角点 ({board_size[0]+1}x{board_size[1]+1} 方格)")
                
                # 显示结果
                display_img = img.copy()
                cv2.drawChessboardCorners(display_img, board_size, corners, True)
                cv2.putText(display_img, f"Detected: {board_size[0]}x{board_size[1]}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                
                cv2.imshow(f'Detection Result: {board_size[0]}x{board_size[1]}', display_img)
                cv2.waitKey(2000)  # 显示2秒
                cv2.destroyAllWindows()
            else:
                print(f"❌ 检测失败: {board_size[0]}x{board_size[1]} 内角点")

def main():
    parser = argparse.ArgumentParser(description='棋盘格检测调试工具')
    parser.add_argument('--camera', type=int, default=0, help='摄像头ID')
    parser.add_argument('--image', help='测试图像文件路径')
    
    args = parser.parse_args()
    
    debugger = ChessboardDebugger()
    
    if args.image:
        debugger.test_image_file(args.image)
    else:
        debugger.debug_detection(args.camera)

if __name__ == "__main__":
    main()
