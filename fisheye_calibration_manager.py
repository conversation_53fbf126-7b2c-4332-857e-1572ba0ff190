#!/usr/bin/env python3
"""
鱼眼摄像头标定管理工具
逐个标定4个鱼眼摄像头，避免冲突
"""

import os
import sys
import time
import subprocess
import json
from datetime import datetime

class FisheyeCalibrationManager:
    def __init__(self):
        self.cameras = [0, 2, 4, 6]  # 4个鱼眼摄像头
        self.calibration_script = "fisheye_calibration.py"
        self.results_dir = "calibration_results"
        
        # 确保目录存在
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def check_camera_availability(self, camera_id):
        """检查摄像头是否可用"""
        try:
            import cv2
            cap = cv2.VideoCapture(camera_id)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                return ret
            return False
        except Exception as e:
            print(f"检查摄像头 {camera_id} 时出错: {e}")
            return False
    
    def kill_camera_processes(self):
        """关闭可能占用摄像头的进程"""
        print("关闭可能占用摄像头的进程...")
        
        processes_to_kill = [
            "usb_cam",
            "opencv",
            "cv2",
            "fisheye_camera_tools",
            "simple_camera_viewer"
        ]
        
        for process in processes_to_kill:
            try:
                subprocess.run(["pkill", "-f", process], 
                             capture_output=True, text=True)
            except:
                pass
        
        time.sleep(2)  # 等待进程完全关闭
        print("进程清理完成")
    
    def run_calibration_step(self, camera_id, step, **kwargs):
        """运行标定步骤"""
        cmd = ["python3", self.calibration_script, "--camera", str(camera_id)]
        
        if step == "capture":
            cmd.extend(["--capture"])
            if "num_images" in kwargs:
                cmd.extend(["--num-images", str(kwargs["num_images"])])
        elif step == "calibrate":
            cmd.extend(["--calibrate"])
        elif step == "test":
            cmd.extend(["--test"])
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=False, text=True)
            return result.returncode == 0
        except Exception as e:
            print(f"执行失败: {e}")
            return False
    
    def calibrate_single_camera(self, camera_id, num_images=25):
        """标定单个摄像头的完整流程"""
        print(f"\n{'='*60}")
        print(f"开始标定摄像头 {camera_id}")
        print(f"{'='*60}")
        
        # 1. 检查摄像头可用性
        print(f"1. 检查摄像头 {camera_id} 可用性...")
        if not self.check_camera_availability(camera_id):
            print(f"❌ 摄像头 {camera_id} 不可用")
            return False
        print(f"✅ 摄像头 {camera_id} 可用")
        
        # 2. 清理进程
        self.kill_camera_processes()
        
        # 3. 拍摄标定图像
        print(f"\n2. 拍摄标定图像 ({num_images} 张)...")
        print("请准备好棋盘格标定板 (8x11内角点, 60mm方格)")
        input("按回车键开始拍摄...")
        
        if not self.run_calibration_step(camera_id, "capture", num_images=num_images):
            print(f"❌ 摄像头 {camera_id} 标定图像拍摄失败")
            return False
        print(f"✅ 摄像头 {camera_id} 标定图像拍摄完成")
        
        # 4. 执行标定
        print(f"\n3. 执行标定计算...")
        if not self.run_calibration_step(camera_id, "calibrate"):
            print(f"❌ 摄像头 {camera_id} 标定计算失败")
            return False
        print(f"✅ 摄像头 {camera_id} 标定计算完成")
        
        # 5. 测试标定结果
        print(f"\n4. 测试标定结果...")
        print("将显示实时校正效果，按 'q' 退出测试")
        input("按回车键开始测试...")
        
        if not self.run_calibration_step(camera_id, "test"):
            print(f"⚠️ 摄像头 {camera_id} 标定测试可能有问题")
        else:
            print(f"✅ 摄像头 {camera_id} 标定测试完成")
        
        # 6. 检查标定结果
        calibration_file = self.find_latest_calibration(camera_id)
        if calibration_file:
            self.show_calibration_summary(camera_id, calibration_file)
            return True
        else:
            print(f"❌ 未找到摄像头 {camera_id} 的标定结果文件")
            return False
    
    def find_latest_calibration(self, camera_id):
        """查找最新的标定文件"""
        import glob
        pattern = os.path.join(self.results_dir, f"fisheye_calib_cam{camera_id}_*.json")
        files = glob.glob(pattern)
        if files:
            return max(files, key=os.path.getctime)
        return None
    
    def show_calibration_summary(self, camera_id, calibration_file):
        """显示标定结果摘要"""
        try:
            with open(calibration_file, 'r') as f:
                data = json.load(f)
            
            print(f"\n📊 摄像头 {camera_id} 标定结果摘要:")
            print(f"  - 标定文件: {os.path.basename(calibration_file)}")
            print(f"  - RMS误差: {data.get('rms_error', 'N/A'):.4f} 像素")
            print(f"  - 标定图像数量: {data.get('num_images', 'N/A')}")
            print(f"  - 标定日期: {data.get('calibration_date', 'N/A')}")
            
            rms = data.get('rms_error', 999)
            if rms < 0.5:
                print(f"  - 质量评估: 🌟 优秀 (RMS < 0.5)")
            elif rms < 1.0:
                print(f"  - 质量评估: ✅ 良好 (RMS < 1.0)")
            elif rms < 2.0:
                print(f"  - 质量评估: ⚠️ 可接受 (RMS < 2.0)")
            else:
                print(f"  - 质量评估: ❌ 需要重新标定 (RMS > 2.0)")
                
        except Exception as e:
            print(f"读取标定结果失败: {e}")
    
    def calibrate_all_cameras(self, num_images=25):
        """标定所有摄像头"""
        print("🚀 开始标定所有鱼眼摄像头")
        print(f"摄像头列表: {self.cameras}")
        print(f"每个摄像头拍摄 {num_images} 张标定图像")
        
        results = {}
        
        for i, camera_id in enumerate(self.cameras, 1):
            print(f"\n🎯 进度: {i}/{len(self.cameras)} - 摄像头 {camera_id}")
            
            success = self.calibrate_single_camera(camera_id, num_images)
            results[camera_id] = success
            
            if success:
                print(f"✅ 摄像头 {camera_id} 标定成功")
            else:
                print(f"❌ 摄像头 {camera_id} 标定失败")
            
            # 如果不是最后一个摄像头，询问是否继续
            if i < len(self.cameras):
                print(f"\n摄像头 {camera_id} 标定完成")
                choice = input("继续标定下一个摄像头? (y/n/s=跳过): ").strip().lower()
                if choice == 'n':
                    break
                elif choice == 's':
                    print(f"跳过摄像头 {self.cameras[i]}")
                    continue
        
        # 显示总结
        self.show_final_summary(results)
        return results
    
    def show_final_summary(self, results):
        """显示最终标定总结"""
        print(f"\n{'='*60}")
        print("🎉 标定完成总结")
        print(f"{'='*60}")
        
        successful = [cam for cam, success in results.items() if success]
        failed = [cam for cam, success in results.items() if not success]
        
        print(f"✅ 成功标定: {len(successful)} 个摄像头 {successful}")
        if failed:
            print(f"❌ 标定失败: {len(failed)} 个摄像头 {failed}")
        
        print(f"\n📁 标定文件保存在: {self.results_dir}/")
        
        # 显示每个成功标定的摄像头的详细信息
        for camera_id in successful:
            calibration_file = self.find_latest_calibration(camera_id)
            if calibration_file:
                self.show_calibration_summary(camera_id, calibration_file)
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print(f"\n{'='*50}")
            print("🐟 鱼眼摄像头标定管理器")
            print(f"{'='*50}")
            print("1. 标定单个摄像头")
            print("2. 标定所有摄像头")
            print("3. 查看标定状态")
            print("4. 测试标定结果")
            print("5. 清理摄像头进程")
            print("0. 退出")
            
            try:
                choice = input("\n请选择 (0-5): ").strip()
                
                if choice == '0':
                    print("👋 再见!")
                    break
                elif choice == '1':
                    camera_id = int(input("输入摄像头ID (0,2,4,6): "))
                    if camera_id in self.cameras:
                        num_images = int(input("标定图像数量 (推荐25): ") or "25")
                        self.calibrate_single_camera(camera_id, num_images)
                    else:
                        print("❌ 无效的摄像头ID")
                elif choice == '2':
                    num_images = int(input("每个摄像头的标定图像数量 (推荐25): ") or "25")
                    self.calibrate_all_cameras(num_images)
                elif choice == '3':
                    self.show_calibration_status()
                elif choice == '4':
                    camera_id = int(input("输入要测试的摄像头ID (0,2,4,6): "))
                    if camera_id in self.cameras:
                        self.run_calibration_step(camera_id, "test")
                    else:
                        print("❌ 无效的摄像头ID")
                elif choice == '5':
                    self.kill_camera_processes()
                else:
                    print("❌ 无效选择")
                    
            except (ValueError, KeyboardInterrupt):
                print("\n👋 程序退出")
                break
    
    def show_calibration_status(self):
        """显示所有摄像头的标定状态"""
        print(f"\n📊 摄像头标定状态:")
        print("-" * 50)
        
        for camera_id in self.cameras:
            calibration_file = self.find_latest_calibration(camera_id)
            if calibration_file:
                print(f"✅ 摄像头 {camera_id}: 已标定")
                self.show_calibration_summary(camera_id, calibration_file)
            else:
                print(f"❌ 摄像头 {camera_id}: 未标定")
        print("-" * 50)

def main():
    manager = FisheyeCalibrationManager()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--all":
            num_images = int(sys.argv[2]) if len(sys.argv) > 2 else 25
            manager.calibrate_all_cameras(num_images)
        elif sys.argv[1] == "--camera":
            camera_id = int(sys.argv[2])
            num_images = int(sys.argv[3]) if len(sys.argv) > 3 else 25
            manager.calibrate_single_camera(camera_id, num_images)
        elif sys.argv[1] == "--status":
            manager.show_calibration_status()
        else:
            print("用法:")
            print("  python3 fisheye_calibration_manager.py                    # 交互式菜单")
            print("  python3 fisheye_calibration_manager.py --all [num_images] # 标定所有摄像头")
            print("  python3 fisheye_calibration_manager.py --camera ID [num]  # 标定单个摄像头")
            print("  python3 fisheye_calibration_manager.py --status           # 查看标定状态")
    else:
        manager.interactive_menu()

if __name__ == "__main__":
    main()
