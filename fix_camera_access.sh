#!/bin/bash
# 摄像头访问修复脚本

echo "🔧 修复摄像头访问问题..."

echo "1. 关闭占用摄像头的进程..."
sudo pkill -f usb_cam 2>/dev/null
sudo pkill -f opencv 2>/dev/null
sudo pkill -f cv2 2>/dev/null
sudo pkill -f python3.*camera 2>/dev/null
sudo pkill -f python3.*fisheye 2>/dev/null

echo "2. 等待进程关闭..."
sleep 3

echo "3. 检查摄像头设备..."
ls -la /dev/video* 2>/dev/null || echo "❌ 未找到video设备"

echo "4. 检查设备占用..."
sudo lsof /dev/video* 2>/dev/null || echo "✅ 没有进程占用摄像头"

echo "5. 重新加载摄像头驱动..."
sudo rmmod uvcvideo 2>/dev/null
sleep 2
sudo modprobe uvcvideo

echo "6. 等待设备初始化..."
sleep 3

echo "7. 检查用户权限..."
if groups $USER | grep -q video; then
    echo "✅ 用户已在video组"
else
    echo "⚠️ 添加用户到video组..."
    sudo usermod -a -G video $USER
    echo "⚠️ 需要重新登录才能生效"
fi

echo "8. 测试摄像头访问..."
python3 << 'EOF'
import cv2
import sys

cameras = [0, 2, 4, 6]
working_cameras = []

for cam_id in cameras:
    try:
        cap = cv2.VideoCapture(cam_id)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                print(f"✅ Camera {cam_id}: 工作正常")
                working_cameras.append(cam_id)
            else:
                print(f"⚠️ Camera {cam_id}: 可打开但无法读取")
        else:
            print(f"❌ Camera {cam_id}: 无法打开")
        cap.release()
    except Exception as e:
        print(f"❌ Camera {cam_id}: 错误 - {e}")

print(f"\n📊 总结: {len(working_cameras)}/4 个摄像头工作正常")
if working_cameras:
    print(f"可用摄像头: {working_cameras}")
else:
    print("❌ 没有可用的摄像头")
    sys.exit(1)
EOF

echo "🎉 修复完成!"
