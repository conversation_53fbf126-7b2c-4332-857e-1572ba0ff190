#!/usr/bin/env python3
"""
交互式鱼眼摄像头标定工具
实时预览摄像头画面，手动选择保存标定图像
"""

import cv2
import numpy as np
import os
import glob
import json
import argparse
from datetime import datetime

class InteractiveFisheyeCalibration:
    def __init__(self):
        self.calibration_dir = "fisheye_calibration"
        self.results_dir = "calibration_results"
        
        # 创建目录
        for dir_name in [self.calibration_dir, self.results_dir]:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
        
        # 标定板参数 (根据用户的棋盘格)
        # 如果检测失败，可以尝试其他常见尺寸
        self.possible_board_sizes = [
            (8, 11),  # 用户描述的尺寸
            (11, 8),  # 旋转90度
            (9, 6),   # 常见尺寸
            (8, 6),
            (7, 5),
        ]
        self.board_size = (8, 11)  # 默认尺寸
        self.square_size = 60.0    # 方格大小 (毫米) - 6cm
        
        # 标定参数
        self.calibration_flags = (cv2.fisheye.CALIB_RECOMPUTE_EXTRINSIC +
                                cv2.fisheye.CALIB_CHECK_COND +
                                cv2.fisheye.CALIB_FIX_SKEW)
        
        # 存储标定数据
        self.object_points = []  # 3D点
        self.image_points = []   # 2D点
        self.image_size = None
    
    def create_object_points(self):
        """创建标定板的3D坐标点"""
        objp = np.zeros((self.board_size[0] * self.board_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:self.board_size[0], 0:self.board_size[1]].T.reshape(-1, 2)
        objp *= self.square_size
        return objp
    
    def interactive_capture(self, camera_id, target_images=25):
        """交互式拍摄标定图像"""
        print(f"🎯 开始交互式标定图像拍摄 - 摄像头 {camera_id}")
        print(f"目标: {target_images} 张标定图像")
        print("\n📋 操作说明:")
        print("  S - 保存当前帧作为标定图像")
        print("  D - 删除最后保存的图像")
        print("  R - 重新开始 (删除所有已保存图像)")
        print("  Q - 完成拍摄")
        print("  H - 显示帮助")
        print("\n🎯 拍摄要求:")
        print("  - 棋盘格必须完全在视野内")
        print("  - 从不同角度和距离拍摄")
        print("  - 确保棋盘格平整，角点清晰")
        print("  - 绿色角点表示检测成功")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"❌ 无法打开摄像头 {camera_id}")
            return False
        
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        captured_count = 0
        saved_files = []
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头数据")
                break
            
            frame_count += 1
            display_frame = frame.copy()
            
            # 检测棋盘格角点 - 尝试多种尺寸
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            ret_corners, corners = False, None
            detected_board_size = None

            # 尝试所有可能的棋盘格尺寸
            for board_size in self.possible_board_sizes:
                ret_temp, corners_temp = cv2.findChessboardCorners(gray, board_size, None)
                if ret_temp:
                    ret_corners, corners = ret_temp, corners_temp
                    detected_board_size = board_size
                    # 更新当前使用的尺寸
                    if self.board_size != board_size:
                        self.board_size = board_size
                        print(f"🔄 自动切换到棋盘格尺寸: {board_size[0]}x{board_size[1]} 内角点")
                    break
            
            # 显示检测状态
            if ret_corners:
                # 精确化角点
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                
                # 绘制检测到的角点
                cv2.drawChessboardCorners(display_frame, self.board_size, corners_refined, True)
                
                # 显示成功信息
                cv2.putText(display_frame, "CHESSBOARD DETECTED!", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                cv2.putText(display_frame, f"Corners: {self.board_size[0]}x{self.board_size[1]}", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(display_frame, "Press 'S' to save this image", (10, 110),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                # 绿色边框表示可以保存
                cv2.rectangle(display_frame, (5, 5), (display_frame.shape[1]-5, display_frame.shape[0]-5), 
                             (0, 255, 0), 5)
            else:
                # 显示未检测到的信息
                cv2.putText(display_frame, "No chessboard found", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
                cv2.putText(display_frame, "Adjust position/angle/lighting", (10, 70),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                
                # 红色边框表示无法保存
                cv2.rectangle(display_frame, (5, 5), (display_frame.shape[1]-5, display_frame.shape[0]-5), 
                             (0, 0, 255), 5)
            
            # 显示进度信息
            cv2.putText(display_frame, f"Captured: {captured_count}/{target_images}", (10, 150),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(display_frame, f"Camera: {camera_id}", (10, 180),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(display_frame, f"Frame: {frame_count}", (10, 210),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 显示控制信息
            y_offset = display_frame.shape[0] - 120
            cv2.putText(display_frame, "Controls: S=Save D=Delete R=Reset Q=Quit H=Help", 
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 显示拍摄提示
            if captured_count < target_images:
                remaining = target_images - captured_count
                cv2.putText(display_frame, f"Need {remaining} more images", 
                           (10, y_offset + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            else:
                cv2.putText(display_frame, "Target reached! Press Q to finish", 
                           (10, y_offset + 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            cv2.imshow(f'Fisheye Calibration - Camera {camera_id}', display_frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):
                if captured_count >= 10:
                    print(f"✅ 拍摄完成: {captured_count} 张图像")
                    break
                else:
                    print(f"⚠️ 至少需要10张图像，当前只有 {captured_count} 张")
                    choice = input("确定要退出吗? (y/n): ").strip().lower()
                    if choice == 'y':
                        break
                        
            elif key == ord('s') and ret_corners:
                # 保存标定图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"calib_cam{camera_id}_{captured_count:02d}_{timestamp}.jpg"
                filepath = os.path.join(self.calibration_dir, filename)
                
                cv2.imwrite(filepath, frame)
                saved_files.append(filepath)
                captured_count += 1
                
                print(f"📸 保存第 {captured_count} 张: {filename}")
                
                # 短暂显示保存成功
                save_frame = display_frame.copy()
                cv2.putText(save_frame, "IMAGE SAVED!", (display_frame.shape[1]//2 - 100, display_frame.shape[0]//2),
                           cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 255, 0), 3)
                cv2.imshow(f'Fisheye Calibration - Camera {camera_id}', save_frame)
                cv2.waitKey(500)  # 显示0.5秒
                
            elif key == ord('d') and saved_files:
                # 删除最后保存的图像
                last_file = saved_files.pop()
                if os.path.exists(last_file):
                    os.remove(last_file)
                captured_count -= 1
                print(f"🗑️ 删除最后一张图像: {os.path.basename(last_file)}")
                
            elif key == ord('r'):
                # 重新开始
                choice = input(f"\n确定要删除所有 {captured_count} 张已保存的图像吗? (y/n): ").strip().lower()
                if choice == 'y':
                    for filepath in saved_files:
                        if os.path.exists(filepath):
                            os.remove(filepath)
                    saved_files.clear()
                    captured_count = 0
                    print("🔄 已重新开始，所有图像已删除")
                    
            elif key == ord('h'):
                # 显示帮助
                print("\n📋 详细操作说明:")
                print("  S - 保存当前帧 (仅在检测到棋盘格时有效)")
                print("  D - 删除最后保存的图像")
                print("  R - 重新开始 (删除所有已保存图像)")
                print("  Q - 完成拍摄 (至少需要10张图像)")
                print("  H - 显示此帮助信息")
                print("\n🎯 拍摄技巧:")
                print("  - 从不同角度拍摄: 正面、左倾、右倾、上倾、下倾")
                print("  - 不同距离: 近距离、中距离、远距离")
                print("  - 不同位置: 中心、四角、边缘")
                print("  - 确保棋盘格完全在视野内")
                print("  - 避免运动模糊和反光")
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 拍摄总结:")
        print(f"  - 成功保存: {captured_count} 张标定图像")
        print(f"  - 保存位置: {self.calibration_dir}/")
        
        return captured_count >= 10
    
    def calibrate_from_images(self, camera_id):
        """从保存的图像执行标定"""
        print(f"🔧 开始标定摄像头 {camera_id}...")
        
        # 查找标定图像
        pattern = os.path.join(self.calibration_dir, f"calib_cam{camera_id}_*.jpg")
        image_files = glob.glob(pattern)
        
        if not image_files:
            print(f"❌ 未找到摄像头 {camera_id} 的标定图像")
            return None
        
        print(f"📁 找到 {len(image_files)} 张标定图像")
        
        # 创建3D对象点
        objp = self.create_object_points()
        
        # 重置标定数据
        self.object_points = []
        self.image_points = []
        
        successful_detections = 0
        
        for img_file in sorted(image_files):
            # 读取图像
            img = cv2.imread(img_file)
            if img is None:
                continue
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 存储图像尺寸
            if self.image_size is None:
                self.image_size = gray.shape[::-1]
            
            # 检测棋盘格角点
            ret, corners = cv2.findChessboardCorners(gray, self.board_size, None)
            
            if ret:
                # 精确化角点位置
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                
                # 存储对象点和图像点
                self.object_points.append(objp)
                self.image_points.append(corners_refined)
                successful_detections += 1
                
                print(f"✅ {os.path.basename(img_file)}: 检测到角点")
            else:
                print(f"❌ {os.path.basename(img_file)}: 未检测到角点")
        
        print(f"📊 成功检测角点的图像: {successful_detections}/{len(image_files)}")
        
        if successful_detections < 10:
            print("❌ 标定图像不足，至少需要10张成功检测角点的图像")
            return None
        
        # 执行标定
        return self.perform_calibration(camera_id, successful_detections)
    
    def perform_calibration(self, camera_id, num_images):
        """执行鱼眼标定计算"""
        print(f"🧮 执行鱼眼标定计算...")
        
        # 初始化相机矩阵和畸变系数
        K = np.zeros((3, 3))
        D = np.zeros((4, 1))
        
        # 转换数据格式
        object_points = [np.array(pts, dtype=np.float32) for pts in self.object_points]
        image_points = [np.array(pts, dtype=np.float32) for pts in self.image_points]
        
        try:
            # 执行鱼眼标定
            rms, K, D, rvecs, tvecs = cv2.fisheye.calibrate(
                object_points,
                image_points,
                self.image_size,
                K,
                D,
                flags=self.calibration_flags,
                criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
            )
            
            print(f"✅ 标定完成!")
            print(f"📊 重投影误差 (RMS): {rms:.4f} 像素")
            
            # 评估标定质量
            if rms < 0.5:
                quality = "🌟 优秀"
            elif rms < 1.0:
                quality = "✅ 良好"
            elif rms < 2.0:
                quality = "⚠️ 可接受"
            else:
                quality = "❌ 需要重新标定"
            
            print(f"📈 标定质量: {quality}")
            
            # 保存标定结果
            calibration_data = {
                'camera_id': camera_id,
                'rms_error': float(rms),
                'camera_matrix': K.tolist(),
                'distortion_coefficients': D.tolist(),
                'image_size': self.image_size,
                'calibration_date': datetime.now().isoformat(),
                'num_images': num_images,
                'board_size': self.board_size,
                'square_size': self.square_size
            }
            
            # 保存为JSON文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = os.path.join(self.results_dir, f"fisheye_calib_cam{camera_id}_{timestamp}.json")
            
            with open(result_file, 'w') as f:
                json.dump(calibration_data, f, indent=2)
            
            print(f"💾 标定结果已保存: {result_file}")
            
            return calibration_data
            
        except Exception as e:
            print(f"❌ 标定失败: {e}")
            return None
    
    def full_calibration_process(self, camera_id, target_images=25):
        """完整的标定流程"""
        print(f"🚀 开始摄像头 {camera_id} 的完整标定流程")
        
        # 1. 交互式拍摄
        if not self.interactive_capture(camera_id, target_images):
            print("❌ 标定图像拍摄失败")
            return None
        
        # 2. 执行标定
        result = self.calibrate_from_images(camera_id)
        
        if result:
            print(f"🎉 摄像头 {camera_id} 标定成功!")
            return result
        else:
            print(f"❌ 摄像头 {camera_id} 标定失败")
            return None

def main():
    parser = argparse.ArgumentParser(description='交互式鱼眼摄像头标定工具')
    parser.add_argument('--camera', type=int, required=True, help='摄像头ID')
    parser.add_argument('--images', type=int, default=25, help='目标标定图像数量')
    parser.add_argument('--capture-only', action='store_true', help='仅拍摄图像，不执行标定')
    parser.add_argument('--calibrate-only', action='store_true', help='仅执行标定，使用已有图像')
    
    args = parser.parse_args()
    
    calibrator = InteractiveFisheyeCalibration()
    
    if args.capture_only:
        calibrator.interactive_capture(args.camera, args.images)
    elif args.calibrate_only:
        calibrator.calibrate_from_images(args.camera)
    else:
        calibrator.full_calibration_process(args.camera, args.images)

if __name__ == "__main__":
    main()
