#!/usr/bin/env python3
"""
鱼眼图像校正和处理工具
支持鱼眼图像去畸变、透视校正、全景拼接等功能
"""

import cv2
import numpy as np
import os
import argparse
from datetime import datetime

class FisheyeCorrection:
    def __init__(self):
        self.save_dir = "fisheye_corrected"
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
    
    def create_fisheye_map(self, img_shape, camera_matrix=None, dist_coeffs=None):
        """创建鱼眼校正映射"""
        h, w = img_shape[:2]
        
        # 默认鱼眼相机参数 (需要根据实际相机标定)
        if camera_matrix is None:
            # 估算的相机内参矩阵
            fx = fy = w * 0.7  # 焦距估算
            cx, cy = w // 2, h // 2  # 光心
            camera_matrix = np.array([[fx, 0, cx],
                                    [0, fy, cy],
                                    [0, 0, 1]], dtype=np.float32)
        
        if dist_coeffs is None:
            # 鱼眼畸变系数 (k1, k2, k3, k4)
            dist_coeffs = np.array([-0.2, 0.1, 0.0, 0.0], dtype=np.float32)
        
        # 创建新的相机矩阵
        new_camera_matrix = cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
            camera_matrix, dist_coeffs, (w, h), np.eye(3), balance=0.6)
        
        # 计算校正映射
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            camera_matrix, dist_coeffs, np.eye(3), new_camera_matrix, (w, h), cv2.CV_16SC2)
        
        return map1, map2, new_camera_matrix
    
    def undistort_fisheye(self, image, map1=None, map2=None):
        """鱼眼图像去畸变"""
        if map1 is None or map2 is None:
            map1, map2, _ = self.create_fisheye_map(image.shape)
        
        # 应用校正映射
        undistorted = cv2.remap(image, map1, map2, cv2.INTER_LINEAR)
        return undistorted
    
    def extract_center_region(self, image, scale=0.8):
        """提取鱼眼图像的中心区域 (去除边缘黑色区域)"""
        h, w = image.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # 计算裁剪区域
        crop_w = int(w * scale)
        crop_h = int(h * scale)
        
        x1 = center_x - crop_w // 2
        y1 = center_y - crop_h // 2
        x2 = x1 + crop_w
        y2 = y1 + crop_h
        
        # 确保坐标在图像范围内
        x1 = max(0, x1)
        y1 = max(0, y1)
        x2 = min(w, x2)
        y2 = min(h, y2)
        
        return image[y1:y2, x1:x2]
    
    def create_perspective_view(self, fisheye_image, fov=90, output_size=(640, 480)):
        """从鱼眼图像创建透视视图"""
        h, w = fisheye_image.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # 创建输出图像
        output_h, output_w = output_size
        perspective_img = np.zeros((output_h, output_w, 3), dtype=np.uint8)
        
        # 计算映射
        for y in range(output_h):
            for x in range(output_w):
                # 将输出坐标转换为角度
                theta = (x - output_w // 2) * fov / output_w * np.pi / 180
                phi = (y - output_h // 2) * fov / output_h * np.pi / 180
                
                # 计算鱼眼图像中的对应坐标
                r = np.sqrt(theta**2 + phi**2)
                if r > 0:
                    # 鱼眼投影
                    rho = 2 * np.tan(r / 2) * min(w, h) / 4
                    
                    # 转换为图像坐标
                    src_x = int(center_x + rho * np.cos(np.arctan2(phi, theta)))
                    src_y = int(center_y + rho * np.sin(np.arctan2(phi, theta)))
                    
                    # 检查坐标是否在图像范围内
                    if 0 <= src_x < w and 0 <= src_y < h:
                        perspective_img[y, x] = fisheye_image[src_y, src_x]
        
        return perspective_img
    
    def process_fisheye_image(self, image_path, correction_type="undistort"):
        """处理单个鱼眼图像"""
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            return None
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
        
        print(f"处理图像: {image_path}")
        print(f"原始尺寸: {image.shape[1]}x{image.shape[0]}")
        
        # 获取文件名 (不含扩展名)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {}
        
        if correction_type in ["undistort", "all"]:
            # 鱼眼去畸变
            print("执行鱼眼去畸变...")
            undistorted = self.undistort_fisheye(image)
            
            # 提取中心区域
            center_region = self.extract_center_region(undistorted, scale=0.8)
            
            # 保存结果
            undistort_path = os.path.join(self.save_dir, f"{base_name}_undistorted_{timestamp}.jpg")
            center_path = os.path.join(self.save_dir, f"{base_name}_center_{timestamp}.jpg")
            
            cv2.imwrite(undistort_path, undistorted)
            cv2.imwrite(center_path, center_region)
            
            results['undistorted'] = undistort_path
            results['center'] = center_path
            
            print(f"去畸变图像已保存: {undistort_path}")
            print(f"中心区域已保存: {center_path}")
        
        if correction_type in ["perspective", "all"]:
            # 创建透视视图
            print("创建透视视图...")
            perspective = self.create_perspective_view(image, fov=90, output_size=(640, 480))
            
            perspective_path = os.path.join(self.save_dir, f"{base_name}_perspective_{timestamp}.jpg")
            cv2.imwrite(perspective_path, perspective)
            
            results['perspective'] = perspective_path
            print(f"透视视图已保存: {perspective_path}")
        
        if correction_type in ["enhanced", "all"]:
            # 增强处理
            print("执行图像增强...")
            
            # 直方图均衡化
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            lab[:,:,0] = cv2.equalizeHist(lab[:,:,0])
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
            # 锐化
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            enhanced_path = os.path.join(self.save_dir, f"{base_name}_enhanced_{timestamp}.jpg")
            cv2.imwrite(enhanced_path, sharpened)
            
            results['enhanced'] = enhanced_path
            print(f"增强图像已保存: {enhanced_path}")
        
        return results
    
    def process_all_fisheye_images(self, input_dir, correction_type="undistort"):
        """批量处理鱼眼图像"""
        if not os.path.exists(input_dir):
            print(f"输入目录不存在: {input_dir}")
            return
        
        # 查找图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        for file in os.listdir(input_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(input_dir, file))
        
        if not image_files:
            print(f"在目录 {input_dir} 中未找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        # 批量处理
        for i, image_path in enumerate(image_files, 1):
            print(f"\n处理第 {i}/{len(image_files)} 个图像...")
            self.process_fisheye_image(image_path, correction_type)
        
        print(f"\n批量处理完成，结果保存在: {self.save_dir}")

def main():
    parser = argparse.ArgumentParser(description='鱼眼图像校正工具')
    parser.add_argument('--image', help='单个图像文件路径')
    parser.add_argument('--dir', help='图像目录路径')
    parser.add_argument('--type', choices=['undistort', 'perspective', 'enhanced', 'all'], 
                       default='undistort', help='校正类型')
    
    args = parser.parse_args()
    
    corrector = FisheyeCorrection()
    
    if args.image:
        corrector.process_fisheye_image(args.image, args.type)
    elif args.dir:
        corrector.process_all_fisheye_images(args.dir, args.type)
    else:
        # 默认处理fisheye_captures目录
        default_dir = "fisheye_captures"
        if os.path.exists(default_dir):
            print(f"处理默认目录: {default_dir}")
            corrector.process_all_fisheye_images(default_dir, args.type)
        else:
            print("请指定 --image 或 --dir 参数")
            print("或确保 fisheye_captures 目录存在")

if __name__ == "__main__":
    main()
