#!/usr/bin/env python3
"""
专业的鱼眼摄像头标定工具
基于OpenCV的鱼眼标定算法，获取准确的相机内参和畸变系数
"""

import cv2
import numpy as np
import os
import glob
import json
import argparse
from datetime import datetime

class FisheyeCalibration:
    def __init__(self):
        self.calibration_dir = "fisheye_calibration"
        self.results_dir = "calibration_results"
        
        # 创建目录
        for dir_name in [self.calibration_dir, self.results_dir]:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
        
        # 标定板参数 (棋盘格)
        self.board_size = (9, 6)  # 内角点数量 (宽, 高)
        self.square_size = 25.0   # 方格大小 (毫米)
        
        # 标定参数
        self.calibration_flags = (cv2.fisheye.CALIB_RECOMPUTE_EXTRINSIC +
                                cv2.fisheye.CALIB_CHECK_COND +
                                cv2.fisheye.CALIB_FIX_SKEW)
        
        # 存储标定数据
        self.object_points = []  # 3D点
        self.image_points = []   # 2D点
        self.image_size = None
    
    def create_object_points(self):
        """创建标定板的3D坐标点"""
        # 创建标定板角点的3D坐标
        objp = np.zeros((self.board_size[0] * self.board_size[1], 3), np.float32)
        objp[:, :2] = np.mgrid[0:self.board_size[0], 0:self.board_size[1]].T.reshape(-1, 2)
        objp *= self.square_size
        return objp
    
    def capture_calibration_images(self, camera_id, num_images=20):
        """拍摄标定图像"""
        print(f"开始拍摄鱼眼摄像头 {camera_id} 的标定图像...")
        print(f"需要拍摄 {num_images} 张图像")
        print("操作说明:")
        print("- 将棋盘格标定板放在摄像头前")
        print("- 按 SPACE 键拍摄图像")
        print("- 按 'q' 键退出")
        print("- 尽量从不同角度和距离拍摄")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return False
        
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        captured_count = 0
        
        while captured_count < num_images:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头数据")
                break
            
            # 显示当前帧
            display_frame = frame.copy()
            cv2.putText(display_frame, f"Camera {camera_id} Calibration", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(display_frame, f"Captured: {captured_count}/{num_images}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(display_frame, "SPACE: Capture, Q: Quit", (10, 110),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 尝试检测棋盘格
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            ret_corners, corners = cv2.findChessboardCorners(gray, self.board_size, None)
            
            if ret_corners:
                # 绘制检测到的角点
                cv2.drawChessboardCorners(display_frame, self.board_size, corners, ret_corners)
                cv2.putText(display_frame, "Chessboard Detected!", (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            else:
                cv2.putText(display_frame, "No Chessboard Found", (10, 150),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            
            cv2.imshow(f'Fisheye Calibration - Camera {camera_id}', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord(' ') and ret_corners:
                # 保存标定图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"calib_cam{camera_id}_{captured_count:02d}_{timestamp}.jpg"
                filepath = os.path.join(self.calibration_dir, filename)
                cv2.imwrite(filepath, frame)
                captured_count += 1
                print(f"保存标定图像: {filename}")
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"标定图像拍摄完成: {captured_count} 张")
        return captured_count > 0
    
    def detect_corners_in_images(self, camera_id):
        """在标定图像中检测角点"""
        print(f"检测摄像头 {camera_id} 标定图像中的角点...")
        
        # 查找标定图像
        pattern = os.path.join(self.calibration_dir, f"calib_cam{camera_id}_*.jpg")
        image_files = glob.glob(pattern)
        
        if not image_files:
            print(f"未找到摄像头 {camera_id} 的标定图像")
            return False
        
        print(f"找到 {len(image_files)} 张标定图像")
        
        # 创建3D对象点
        objp = self.create_object_points()
        
        # 重置标定数据
        self.object_points = []
        self.image_points = []
        
        successful_detections = 0
        
        for img_file in image_files:
            # 读取图像
            img = cv2.imread(img_file)
            if img is None:
                continue
                
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 存储图像尺寸
            if self.image_size is None:
                self.image_size = gray.shape[::-1]
            
            # 检测棋盘格角点
            ret, corners = cv2.findChessboardCorners(gray, self.board_size, None)
            
            if ret:
                # 精确化角点位置
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                
                # 存储对象点和图像点
                self.object_points.append(objp)
                self.image_points.append(corners_refined)
                successful_detections += 1
                
                print(f"✓ {os.path.basename(img_file)}: 检测到角点")
            else:
                print(f"✗ {os.path.basename(img_file)}: 未检测到角点")
        
        print(f"成功检测角点的图像: {successful_detections}/{len(image_files)}")
        return successful_detections >= 10  # 至少需要10张成功的图像
    
    def calibrate_fisheye_camera(self, camera_id):
        """执行鱼眼摄像头标定"""
        print(f"开始标定鱼眼摄像头 {camera_id}...")
        
        if len(self.object_points) < 10:
            print("标定图像不足，至少需要10张成功检测角点的图像")
            return None
        
        # 初始化相机矩阵和畸变系数
        K = np.zeros((3, 3))
        D = np.zeros((4, 1))
        
        # 转换数据格式
        object_points = [np.array(pts, dtype=np.float32) for pts in self.object_points]
        image_points = [np.array(pts, dtype=np.float32) for pts in self.image_points]
        
        print("执行鱼眼标定算法...")
        
        try:
            # 执行鱼眼标定
            rms, K, D, rvecs, tvecs = cv2.fisheye.calibrate(
                object_points,
                image_points,
                self.image_size,
                K,
                D,
                flags=self.calibration_flags,
                criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 1e-6)
            )
            
            print(f"标定完成!")
            print(f"重投影误差 (RMS): {rms:.4f} 像素")
            
            # 保存标定结果
            calibration_data = {
                'camera_id': camera_id,
                'rms_error': float(rms),
                'camera_matrix': K.tolist(),
                'distortion_coefficients': D.tolist(),
                'image_size': self.image_size,
                'calibration_date': datetime.now().isoformat(),
                'num_images': len(object_points),
                'board_size': self.board_size,
                'square_size': self.square_size
            }
            
            # 保存为JSON文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = os.path.join(self.results_dir, f"fisheye_calib_cam{camera_id}_{timestamp}.json")
            
            with open(result_file, 'w') as f:
                json.dump(calibration_data, f, indent=2)
            
            print(f"标定结果已保存: {result_file}")
            
            # 显示标定参数
            print("\n=== 标定结果 ===")
            print(f"相机内参矩阵 K:")
            print(K)
            print(f"\n鱼眼畸变系数 D:")
            print(D.ravel())
            print(f"\n图像尺寸: {self.image_size}")
            print(f"重投影误差: {rms:.4f} 像素")
            
            return calibration_data
            
        except Exception as e:
            print(f"标定失败: {e}")
            return None
    
    def load_calibration_data(self, camera_id):
        """加载标定数据"""
        # 查找最新的标定文件
        pattern = os.path.join(self.results_dir, f"fisheye_calib_cam{camera_id}_*.json")
        calib_files = glob.glob(pattern)
        
        if not calib_files:
            print(f"未找到摄像头 {camera_id} 的标定数据")
            return None
        
        # 使用最新的标定文件
        latest_file = max(calib_files, key=os.path.getctime)
        
        try:
            with open(latest_file, 'r') as f:
                calibration_data = json.load(f)
            
            print(f"加载标定数据: {latest_file}")
            return calibration_data
            
        except Exception as e:
            print(f"加载标定数据失败: {e}")
            return None
    
    def test_calibration(self, camera_id):
        """测试标定结果"""
        calibration_data = self.load_calibration_data(camera_id)
        if calibration_data is None:
            return False
        
        print(f"测试摄像头 {camera_id} 的标定结果...")
        
        # 提取标定参数
        K = np.array(calibration_data['camera_matrix'])
        D = np.array(calibration_data['distortion_coefficients'])
        image_size = tuple(calibration_data['image_size'])
        
        # 打开摄像头进行实时测试
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return False
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, image_size[0])
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, image_size[1])
        
        # 计算校正映射
        new_K = cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
            K, D, image_size, np.eye(3), balance=0.6)
        
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K, D, np.eye(3), new_K, image_size, cv2.CV_16SC2)
        
        print("实时校正测试 - 按 'q' 退出")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 应用校正
            undistorted = cv2.remap(frame, map1, map2, cv2.INTER_LINEAR)
            
            # 并排显示原图和校正后的图像
            combined = np.hstack([frame, undistorted])
            
            # 添加标签
            cv2.putText(combined, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            cv2.putText(combined, "Corrected", (frame.shape[1] + 10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.imshow(f'Fisheye Calibration Test - Camera {camera_id}', combined)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        return True

def main():
    parser = argparse.ArgumentParser(description='鱼眼摄像头标定工具')
    parser.add_argument('--camera', type=int, required=True, help='摄像头ID')
    parser.add_argument('--capture', action='store_true', help='拍摄标定图像')
    parser.add_argument('--calibrate', action='store_true', help='执行标定')
    parser.add_argument('--test', action='store_true', help='测试标定结果')
    parser.add_argument('--num-images', type=int, default=20, help='标定图像数量')
    
    args = parser.parse_args()
    
    calibrator = FisheyeCalibration()
    
    if args.capture:
        calibrator.capture_calibration_images(args.camera, args.num_images)
    
    if args.calibrate:
        if calibrator.detect_corners_in_images(args.camera):
            calibrator.calibrate_fisheye_camera(args.camera)
        else:
            print("角点检测失败，无法进行标定")
    
    if args.test:
        calibrator.test_calibration(args.camera)

if __name__ == "__main__":
    main()
