#!/usr/bin/env python3
"""
批量拍摄所有camera的照片
"""

import cv2
import os
import time
from datetime import datetime
import argparse

def capture_from_camera(camera_id, save_dir="captures", prefix=""):
    """从指定camera拍摄一张照片"""
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"错误: 无法打开Camera {camera_id}")
        return False
        
    # 设置分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    
    # 等待camera稳定
    for _ in range(5):
        ret, frame = cap.read()
        if not ret:
            print(f"Camera {camera_id} 读取失败")
            cap.release()
            return False
    
    # 拍摄照片
    ret, frame = cap.read()
    if ret:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if prefix:
            filename = f"{prefix}_camera_{camera_id}_{timestamp}.jpg"
        else:
            filename = f"camera_{camera_id}_{timestamp}.jpg"
        
        filepath = os.path.join(save_dir, filename)
        cv2.imwrite(filepath, frame)
        
        height, width = frame.shape[:2]
        print(f"Camera {camera_id}: {filepath} ({width}x{height})")
        
    cap.release()
    return ret

def capture_all_cameras(save_dir="captures", prefix=""):
    """拍摄所有可用camera的照片"""
    # 创建保存目录
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"创建目录: {save_dir}")
    
    available_cameras = [0, 2, 4, 6]  # 根据之前的检测结果
    
    print(f"开始批量拍摄 {len(available_cameras)} 个camera...")
    
    success_count = 0
    for camera_id in available_cameras:
        print(f"拍摄Camera {camera_id}...", end=" ")
        if capture_from_camera(camera_id, save_dir, prefix):
            success_count += 1
        else:
            print("失败")
        time.sleep(0.5)  # 短暂延迟避免冲突
    
    print(f"\n批量拍摄完成: {success_count}/{len(available_cameras)} 成功")
    return success_count

def capture_timelapse(camera_id, count=10, interval=1, save_dir="timelapse"):
    """拍摄延时摄影序列"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"创建目录: {save_dir}")
    
    print(f"开始延时拍摄Camera {camera_id}: {count}张照片，间隔{interval}秒")
    
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"错误: 无法打开Camera {camera_id}")
        return False
        
    # 设置分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    
    success_count = 0
    for i in range(count):
        # 等待camera稳定
        for _ in range(3):
            ret, frame = cap.read()
            if not ret:
                break
        
        if ret:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"timelapse_camera_{camera_id}_{i+1:03d}_{timestamp}.jpg"
            filepath = os.path.join(save_dir, filename)
            cv2.imwrite(filepath, frame)
            success_count += 1
            print(f"拍摄 {i+1}/{count}: {filename}")
        else:
            print(f"拍摄 {i+1}/{count}: 失败")
            
        if i < count - 1:  # 最后一张不需要等待
            time.sleep(interval)
    
    cap.release()
    print(f"延时拍摄完成: {success_count}/{count} 成功")
    return success_count == count

def main():
    parser = argparse.ArgumentParser(description='批量camera拍摄工具')
    parser.add_argument('--all', action='store_true', help='拍摄所有camera')
    parser.add_argument('--camera', type=int, help='拍摄指定camera')
    parser.add_argument('--timelapse', type=int, nargs=3, metavar=('CAMERA_ID', 'COUNT', 'INTERVAL'), 
                       help='延时拍摄: camera_id count interval')
    parser.add_argument('--dir', default='captures', help='保存目录 (默认: captures)')
    parser.add_argument('--prefix', default='', help='文件名前缀')
    
    args = parser.parse_args()
    
    if args.all:
        capture_all_cameras(args.dir, args.prefix)
    elif args.camera is not None:
        if not os.path.exists(args.dir):
            os.makedirs(args.dir)
        capture_from_camera(args.camera, args.dir, args.prefix)
    elif args.timelapse:
        camera_id, count, interval = args.timelapse
        capture_timelapse(camera_id, count, interval, args.dir)
    else:
        # 默认行为：拍摄所有camera
        print("未指定参数，默认拍摄所有camera")
        capture_all_cameras(args.dir, args.prefix)

if __name__ == "__main__":
    main()
