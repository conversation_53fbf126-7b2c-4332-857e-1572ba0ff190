#!/usr/bin/env python3
"""
鱼眼摄像头快速启动脚本
提供简单的菜单界面，方便快速使用各种鱼眼摄像头功能
"""

import os
import sys
import subprocess
import time

class FisheyeQuickStart:
    def __init__(self):
        self.base_dir = "/home/<USER>/BeeSwarm"
        self.scripts = {
            'fisheye_tools': 'fisheye_camera_tools.py',
            'fisheye_correction': 'fisheye_correction.py',
            'camera_demo': 'camera_demo.py',
            'simple_viewer': 'simple_camera_viewer.py',
            'batch_capture': 'batch_capture.py'
        }
    
    def run_script(self, script_name, args=""):
        """运行指定脚本"""
        script_path = os.path.join(self.base_dir, self.scripts[script_name])
        if not os.path.exists(script_path):
            print(f"错误: 脚本文件不存在 {script_path}")
            return False
        
        cmd = f"python3 {script_path} {args}"
        print(f"执行命令: {cmd}")
        print("-" * 50)
        
        try:
            # 切换到正确的目录
            os.chdir(self.base_dir)
            result = subprocess.run(cmd, shell=True)
            return result.returncode == 0
        except KeyboardInterrupt:
            print("\n用户中断操作")
            return False
        except Exception as e:
            print(f"执行错误: {e}")
            return False
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*60)
        print("🐟 鱼眼摄像头快速启动菜单")
        print("="*60)
        print("📸 基本功能:")
        print("  1. 实时查看所有鱼眼摄像头 (网格显示)")
        print("  2. 拍摄所有鱼眼摄像头")
        print("  3. 创建鱼眼网格图像")
        print("  4. 拍摄单个鱼眼摄像头")
        print()
        print("🔧 高级功能:")
        print("  5. 创建全景延时序列")
        print("  6. 鱼眼图像校正 (去畸变)")
        print("  7. 创建透视视图")
        print("  8. 图像增强处理")
        print()
        print("🛠️ 工具功能:")
        print("  9. 检测摄像头设备")
        print(" 10. 测试所有摄像头")
        print(" 11. 查看保存的图像")
        print(" 12. 清理旧文件")
        print()
        print("  0. 退出")
        print("="*60)
    
    def handle_choice(self, choice):
        """处理用户选择"""
        if choice == '1':
            print("🎥 启动实时鱼眼网格显示...")
            print("提示: 按 'q' 退出, 按 's' 保存网格, 按 'c' 保存单独图像")
            self.run_script('fisheye_tools', '--live-grid')
            
        elif choice == '2':
            prefix = input("输入文件前缀 (可选，直接回车跳过): ").strip()
            args = '--all'
            if prefix:
                args += f' --prefix "{prefix}"'
            print("📸 拍摄所有鱼眼摄像头...")
            self.run_script('fisheye_tools', args)
            
        elif choice == '3':
            print("🖼️ 创建鱼眼网格图像...")
            self.run_script('fisheye_tools', '--grid')
            
        elif choice == '4':
            camera_id = input("输入摄像头ID (0, 2, 4, 6): ").strip()
            if camera_id in ['0', '2', '4', '6']:
                print(f"📷 拍摄鱼眼摄像头 {camera_id}...")
                self.run_script('fisheye_tools', f'--single {camera_id}')
            else:
                print("❌ 无效的摄像头ID")
                
        elif choice == '5':
            try:
                count = int(input("输入序列数量 (默认10): ") or "10")
                interval = int(input("输入间隔秒数 (默认2): ") or "2")
                print(f"🎬 创建全景延时序列: {count}组, 间隔{interval}秒...")
                self.run_script('fisheye_tools', f'--panorama {count} {interval}')
            except ValueError:
                print("❌ 请输入有效数字")
                
        elif choice == '6':
            print("🔧 执行鱼眼图像去畸变校正...")
            self.run_script('fisheye_correction', '--dir fisheye_captures --type undistort')
            
        elif choice == '7':
            print("👁️ 创建透视视图...")
            self.run_script('fisheye_correction', '--dir fisheye_captures --type perspective')
            
        elif choice == '8':
            print("✨ 执行图像增强处理...")
            self.run_script('fisheye_correction', '--dir fisheye_captures --type enhanced')
            
        elif choice == '9':
            print("🔍 检测摄像头设备...")
            self.run_script('camera_demo', '--detect')
            
        elif choice == '10':
            print("🧪 测试所有摄像头...")
            self.run_script('camera_demo', '--test-all')
            
        elif choice == '11':
            self.show_saved_images()
            
        elif choice == '12':
            self.cleanup_old_files()
            
        elif choice == '0':
            print("👋 再见!")
            return False
            
        else:
            print("❌ 无效选择，请重试")
        
        return True
    
    def show_saved_images(self):
        """显示保存的图像信息"""
        print("\n📁 查看保存的图像...")
        
        directories = [
            'fisheye_captures',
            'fisheye_corrected', 
            'camera_captures',
            'captures',
            'test_captures'
        ]
        
        for dir_name in directories:
            dir_path = os.path.join(self.base_dir, dir_name)
            if os.path.exists(dir_path):
                files = [f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if files:
                    print(f"\n📂 {dir_name}/ ({len(files)} 个文件)")
                    # 显示最新的5个文件
                    files.sort(reverse=True)
                    for file in files[:5]:
                        file_path = os.path.join(dir_path, file)
                        size = os.path.getsize(file_path) // 1024  # KB
                        print(f"   📄 {file} ({size} KB)")
                    if len(files) > 5:
                        print(f"   ... 还有 {len(files) - 5} 个文件")
        
        input("\n按回车键继续...")
    
    def cleanup_old_files(self):
        """清理旧文件"""
        print("\n🧹 清理旧文件...")
        
        directories = [
            'fisheye_captures',
            'fisheye_corrected',
            'camera_captures', 
            'captures',
            'test_captures'
        ]
        
        total_deleted = 0
        total_size = 0
        
        for dir_name in directories:
            dir_path = os.path.join(self.base_dir, dir_name)
            if os.path.exists(dir_path):
                files = [f for f in os.listdir(dir_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if files:
                    print(f"\n📂 {dir_name}/ 有 {len(files)} 个图像文件")
                    choice = input("是否删除? (y/N): ").strip().lower()
                    if choice == 'y':
                        for file in files:
                            file_path = os.path.join(dir_path, file)
                            size = os.path.getsize(file_path)
                            os.remove(file_path)
                            total_deleted += 1
                            total_size += size
                        print(f"✅ 已删除 {len(files)} 个文件")
        
        if total_deleted > 0:
            print(f"\n🎉 清理完成: 删除了 {total_deleted} 个文件，释放 {total_size // 1024 // 1024} MB 空间")
        else:
            print("\n💡 没有文件需要清理")
        
        input("\n按回车键继续...")
    
    def run(self):
        """运行主程序"""
        print("🚀 鱼眼摄像头系统启动中...")
        
        # 检查脚本文件是否存在
        missing_scripts = []
        for name, script in self.scripts.items():
            script_path = os.path.join(self.base_dir, script)
            if not os.path.exists(script_path):
                missing_scripts.append(script)
        
        if missing_scripts:
            print("❌ 以下脚本文件缺失:")
            for script in missing_scripts:
                print(f"   - {script}")
            print(f"\n请确保所有脚本文件都在 {self.base_dir} 目录中")
            return
        
        # 主循环
        while True:
            try:
                self.show_main_menu()
                choice = input("\n请选择功能 (0-12): ").strip()
                
                if not self.handle_choice(choice):
                    break
                    
                if choice != '0':
                    input("\n按回车键返回主菜单...")
                    
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，程序退出")
                break
            except Exception as e:
                print(f"\n❌ 程序错误: {e}")
                input("按回车键继续...")

def main():
    app = FisheyeQuickStart()
    app.run()

if __name__ == "__main__":
    main()
