#!/usr/bin/env python3
"""
基于真实标定数据的高级鱼眼校正工具
使用实际标定的相机参数进行精确的鱼眼畸变校正
"""

import cv2
import numpy as np
import os
import json
import glob
import argparse
from datetime import datetime

class AdvancedFisheyeCorrection:
    def __init__(self):
        self.calibration_dir = "calibration_results"
        self.output_dir = "fisheye_corrected_advanced"
        
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 存储标定数据
        self.calibration_cache = {}
    
    def load_calibration_data(self, camera_id):
        """加载指定摄像头的标定数据"""
        if camera_id in self.calibration_cache:
            return self.calibration_cache[camera_id]
        
        # 查找标定文件
        pattern = os.path.join(self.calibration_dir, f"fisheye_calib_cam{camera_id}_*.json")
        calib_files = glob.glob(pattern)
        
        if not calib_files:
            print(f"警告: 未找到摄像头 {camera_id} 的标定数据，使用默认参数")
            return self.get_default_calibration(camera_id)
        
        # 使用最新的标定文件
        latest_file = max(calib_files, key=os.path.getctime)
        
        try:
            with open(latest_file, 'r') as f:
                calibration_data = json.load(f)
            
            print(f"加载摄像头 {camera_id} 标定数据: {os.path.basename(latest_file)}")
            print(f"  - RMS误差: {calibration_data.get('rms_error', 'N/A'):.4f} 像素")
            print(f"  - 标定日期: {calibration_data.get('calibration_date', 'N/A')}")
            
            # 缓存标定数据
            self.calibration_cache[camera_id] = calibration_data
            return calibration_data
            
        except Exception as e:
            print(f"加载标定数据失败: {e}")
            return self.get_default_calibration(camera_id)
    
    def get_default_calibration(self, camera_id):
        """获取默认标定参数（用于没有标定数据的情况）"""
        print(f"使用摄像头 {camera_id} 的默认标定参数")
        
        # 基于1280x720分辨率的估算参数
        return {
            'camera_id': camera_id,
            'camera_matrix': [
                [896.0, 0.0, 640.0],
                [0.0, 896.0, 360.0],
                [0.0, 0.0, 1.0]
            ],
            'distortion_coefficients': [[-0.2], [0.1], [0.0], [0.0]],
            'image_size': [1280, 720],
            'rms_error': 999.0,  # 表示这是估算值
            'calibration_date': 'default'
        }
    
    def create_undistort_maps(self, calibration_data, balance=0.6):
        """创建鱼眼校正映射"""
        K = np.array(calibration_data['camera_matrix'], dtype=np.float32)
        D = np.array(calibration_data['distortion_coefficients'], dtype=np.float32).reshape(-1)
        image_size = tuple(calibration_data['image_size'])
        
        # 估算新的相机矩阵
        new_K = cv2.fisheye.estimateNewCameraMatrixForUndistortRectify(
            K, D, image_size, np.eye(3), balance=balance)
        
        # 创建校正映射
        map1, map2 = cv2.fisheye.initUndistortRectifyMap(
            K, D, np.eye(3), new_K, image_size, cv2.CV_16SC2)
        
        return map1, map2, new_K
    
    def undistort_image(self, image, calibration_data, balance=0.6):
        """校正单张图像"""
        map1, map2, new_K = self.create_undistort_maps(calibration_data, balance)
        
        # 应用校正
        undistorted = cv2.remap(image, map1, map2, cv2.INTER_LINEAR)
        
        return undistorted, new_K
    
    def process_image_file(self, image_path, camera_id, correction_types=['undistort']):
        """处理单个图像文件"""
        if not os.path.exists(image_path):
            print(f"图像文件不存在: {image_path}")
            return None
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None
        
        print(f"处理图像: {os.path.basename(image_path)}")
        
        # 加载标定数据
        calibration_data = self.load_calibration_data(camera_id)
        
        # 获取文件名
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {}
        
        if 'undistort' in correction_types:
            # 基本校正
            undistorted, new_K = self.undistort_image(image, calibration_data, balance=0.6)
            
            save_path = os.path.join(self.output_dir, f"{base_name}_undistorted_{timestamp}.jpg")
            cv2.imwrite(save_path, undistorted)
            results['undistorted'] = save_path
            print(f"  ✓ 校正图像: {save_path}")
        
        if 'balanced' in correction_types:
            # 平衡校正（保留更多视野）
            undistorted_balanced, _ = self.undistort_image(image, calibration_data, balance=1.0)
            
            save_path = os.path.join(self.output_dir, f"{base_name}_balanced_{timestamp}.jpg")
            cv2.imwrite(save_path, undistorted_balanced)
            results['balanced'] = save_path
            print(f"  ✓ 平衡校正: {save_path}")
        
        if 'cropped' in correction_types:
            # 裁剪校正（去除黑边）
            undistorted_crop, _ = self.undistort_image(image, calibration_data, balance=0.0)
            
            save_path = os.path.join(self.output_dir, f"{base_name}_cropped_{timestamp}.jpg")
            cv2.imwrite(save_path, undistorted_crop)
            results['cropped'] = save_path
            print(f"  ✓ 裁剪校正: {save_path}")
        
        if 'comparison' in correction_types:
            # 创建对比图像
            undistorted, _ = self.undistort_image(image, calibration_data, balance=0.6)
            
            # 调整尺寸以便并排显示
            h, w = image.shape[:2]
            resized_original = cv2.resize(image, (w//2, h//2))
            resized_corrected = cv2.resize(undistorted, (w//2, h//2))
            
            # 创建对比图像
            comparison = np.hstack([resized_original, resized_corrected])
            
            # 添加标签
            cv2.putText(comparison, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            cv2.putText(comparison, "Corrected", (w//2 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            save_path = os.path.join(self.output_dir, f"{base_name}_comparison_{timestamp}.jpg")
            cv2.imwrite(save_path, comparison)
            results['comparison'] = save_path
            print(f"  ✓ 对比图像: {save_path}")
        
        return results
    
    def live_correction(self, camera_id, correction_mode='undistort'):
        """实时鱼眼校正"""
        print(f"启动摄像头 {camera_id} 实时鱼眼校正...")
        
        # 加载标定数据
        calibration_data = self.load_calibration_data(camera_id)
        
        # 打开摄像头
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return False
        
        # 设置分辨率
        image_size = tuple(calibration_data['image_size'])
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, image_size[0])
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, image_size[1])
        
        # 创建校正映射
        balance_values = {'undistort': 0.6, 'balanced': 1.0, 'cropped': 0.0}
        balance = balance_values.get(correction_mode, 0.6)
        
        map1, map2, new_K = self.create_undistort_maps(calibration_data, balance)
        
        print(f"实时校正模式: {correction_mode}")
        print("控制键:")
        print("  'q' - 退出")
        print("  's' - 保存当前帧")
        print("  '1' - 标准校正")
        print("  '2' - 平衡校正")
        print("  '3' - 裁剪校正")
        print("  'c' - 对比模式")
        
        show_comparison = False
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头数据")
                break
            
            # 应用校正
            corrected = cv2.remap(frame, map1, map2, cv2.INTER_LINEAR)
            
            if show_comparison:
                # 显示对比
                h, w = frame.shape[:2]
                resized_original = cv2.resize(frame, (w//2, h//2))
                resized_corrected = cv2.resize(corrected, (w//2, h//2))
                display = np.hstack([resized_original, resized_corrected])
                
                cv2.putText(display, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                cv2.putText(display, "Corrected", (w//2 + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            else:
                # 只显示校正后的图像
                display = corrected
                cv2.putText(display, f"Camera {camera_id} - {correction_mode}", (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            cv2.putText(display, "Press 'h' for help", (10, display.shape[0] - 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow(f'Advanced Fisheye Correction - Camera {camera_id}', display)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                original_path = os.path.join(self.output_dir, f"live_original_cam{camera_id}_{timestamp}.jpg")
                corrected_path = os.path.join(self.output_dir, f"live_corrected_cam{camera_id}_{timestamp}.jpg")
                
                cv2.imwrite(original_path, frame)
                cv2.imwrite(corrected_path, corrected)
                print(f"保存图像: {os.path.basename(corrected_path)}")
            elif key == ord('1'):
                correction_mode = 'undistort'
                map1, map2, new_K = self.create_undistort_maps(calibration_data, 0.6)
                print("切换到标准校正模式")
            elif key == ord('2'):
                correction_mode = 'balanced'
                map1, map2, new_K = self.create_undistort_maps(calibration_data, 1.0)
                print("切换到平衡校正模式")
            elif key == ord('3'):
                correction_mode = 'cropped'
                map1, map2, new_K = self.create_undistort_maps(calibration_data, 0.0)
                print("切换到裁剪校正模式")
            elif key == ord('c'):
                show_comparison = not show_comparison
                print(f"对比模式: {'开启' if show_comparison else '关闭'}")
            elif key == ord('h'):
                print("\n=== 帮助信息 ===")
                print("'q' - 退出")
                print("'s' - 保存当前帧")
                print("'1' - 标准校正 (balance=0.6)")
                print("'2' - 平衡校正 (balance=1.0, 保留更多视野)")
                print("'3' - 裁剪校正 (balance=0.0, 去除黑边)")
                print("'c' - 切换对比模式")
                print("'h' - 显示帮助")
        
        cap.release()
        cv2.destroyAllWindows()
        return True
    
    def batch_process_directory(self, input_dir, camera_id, correction_types=['undistort']):
        """批量处理目录中的图像"""
        if not os.path.exists(input_dir):
            print(f"输入目录不存在: {input_dir}")
            return
        
        # 查找图像文件
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(input_dir, ext)))
            image_files.extend(glob.glob(os.path.join(input_dir, ext.upper())))
        
        if not image_files:
            print(f"在目录 {input_dir} 中未找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        print(f"校正类型: {correction_types}")
        
        # 批量处理
        for i, image_path in enumerate(image_files, 1):
            print(f"\n处理第 {i}/{len(image_files)} 个图像...")
            self.process_image_file(image_path, camera_id, correction_types)
        
        print(f"\n批量处理完成，结果保存在: {self.output_dir}")

def main():
    parser = argparse.ArgumentParser(description='高级鱼眼校正工具')
    parser.add_argument('--camera', type=int, required=True, help='摄像头ID')
    parser.add_argument('--live', action='store_true', help='实时校正')
    parser.add_argument('--image', help='单个图像文件路径')
    parser.add_argument('--dir', help='图像目录路径')
    parser.add_argument('--types', nargs='+', 
                       choices=['undistort', 'balanced', 'cropped', 'comparison'],
                       default=['undistort'], help='校正类型')
    parser.add_argument('--mode', choices=['undistort', 'balanced', 'cropped'],
                       default='undistort', help='实时校正模式')
    
    args = parser.parse_args()
    
    corrector = AdvancedFisheyeCorrection()
    
    if args.live:
        corrector.live_correction(args.camera, args.mode)
    elif args.image:
        corrector.process_image_file(args.image, args.camera, args.types)
    elif args.dir:
        corrector.batch_process_directory(args.dir, args.camera, args.types)
    else:
        print("请指定 --live, --image 或 --dir 参数")

if __name__ == "__main__":
    main()
