#!/usr/bin/env python3
"""
棋盘格检测工具
帮助确定棋盘格的内角点数量和验证检测效果
"""

import cv2
import numpy as np
import argparse

class ChessboardDetector:
    def __init__(self):
        # 常见的棋盘格尺寸
        self.common_sizes = [
            (9, 6),   # 10x7方格
            (8, 6),   # 9x7方格  
            (7, 5),   # 8x6方格
            (6, 4),   # 7x5方格
            (9, 7),   # 10x8方格
            (8, 5),   # 9x6方格
            (10, 7),  # 11x8方格
            (6, 9),   # 7x10方格 (竖向)
            (5, 7),   # 6x8方格 (竖向)
        ]
    
    def detect_chessboard_live(self, camera_id):
        """实时检测棋盘格，自动尝试不同尺寸"""
        print("棋盘格实时检测工具")
        print("操作说明:")
        print("- 将棋盘格放在摄像头前")
        print("- 程序会自动尝试检测不同尺寸的棋盘格")
        print("- 检测成功时会显示绿色角点")
        print("- 按 'q' 退出")
        print("- 按 's' 保存当前检测结果")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return None
        
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        detected_size = None
        detection_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头数据")
                break
            
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            display_frame = frame.copy()
            
            # 尝试检测不同尺寸的棋盘格
            best_corners = None
            best_size = None
            
            for board_size in self.common_sizes:
                ret_corners, corners = cv2.findChessboardCorners(gray, board_size, None)
                
                if ret_corners:
                    # 精确化角点
                    criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                    corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                    
                    best_corners = corners_refined
                    best_size = board_size
                    break  # 找到第一个匹配的就停止
            
            # 显示检测结果
            if best_corners is not None and best_size is not None:
                # 绘制检测到的角点
                cv2.drawChessboardCorners(display_frame, best_size, best_corners, True)
                
                # 显示检测信息
                cv2.putText(display_frame, f"Detected: {best_size[0]}x{best_size[1]} corners", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(display_frame, f"Grid: {best_size[0]+1}x{best_size[1]+1} squares", 
                           (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                cv2.putText(display_frame, "DETECTION SUCCESS!", 
                           (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                # 记录检测结果
                if detected_size != best_size:
                    detected_size = best_size
                    detection_count = 1
                    print(f"检测到棋盘格: {best_size[0]}x{best_size[1]} 内角点 ({best_size[0]+1}x{best_size[1]+1} 方格)")
                else:
                    detection_count += 1
                    if detection_count % 30 == 0:  # 每秒显示一次
                        print(f"持续检测: {best_size[0]}x{best_size[1]} 内角点")
            else:
                # 未检测到棋盘格
                cv2.putText(display_frame, "No chessboard detected", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                cv2.putText(display_frame, "Try different angles/distances", 
                           (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                
                # 显示尝试的尺寸
                sizes_text = "Trying: " + ", ".join([f"{s[0]}x{s[1]}" for s in self.common_sizes[:4]])
                cv2.putText(display_frame, sizes_text, 
                           (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 1)
            
            # 显示控制信息
            cv2.putText(display_frame, "Press 'q' to quit, 's' to save", 
                       (10, display_frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('Chessboard Detection', display_frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s') and best_corners is not None:
                # 保存检测结果
                import time
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"chessboard_detected_{timestamp}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"保存检测结果: {filename}")
                print(f"棋盘格参数: {best_size[0]}x{best_size[1]} 内角点")
        
        cap.release()
        cv2.destroyAllWindows()
        
        return detected_size
    
    def test_chessboard_sizes(self, camera_id):
        """测试所有可能的棋盘格尺寸"""
        print("测试所有可能的棋盘格尺寸...")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return
        
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        print("请将棋盘格放在摄像头前，按任意键开始测试...")
        cv2.waitKey(0)
        
        # 拍摄一张图像进行测试
        ret, frame = cap.read()
        if not ret:
            print("无法拍摄图像")
            cap.release()
            return
        
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        print("\n测试结果:")
        print("-" * 50)
        
        detected_sizes = []
        
        for board_size in self.common_sizes:
            ret_corners, corners = cv2.findChessboardCorners(gray, board_size, None)
            
            if ret_corners:
                # 精确化角点
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners_refined = cv2.cornerSubPix(gray, corners, (11, 11), (-1, -1), criteria)
                
                print(f"✅ {board_size[0]}x{board_size[1]} 内角点 ({board_size[0]+1}x{board_size[1]+1} 方格) - 检测成功")
                detected_sizes.append(board_size)
                
                # 显示检测结果
                display_frame = frame.copy()
                cv2.drawChessboardCorners(display_frame, board_size, corners_refined, True)
                cv2.putText(display_frame, f"{board_size[0]}x{board_size[1]} corners detected", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.imshow(f'Detected: {board_size[0]}x{board_size[1]}', display_frame)
                cv2.waitKey(1000)  # 显示1秒
                cv2.destroyAllWindows()
            else:
                print(f"❌ {board_size[0]}x{board_size[1]} 内角点 ({board_size[0]+1}x{board_size[1]+1} 方格) - 检测失败")
        
        cap.release()
        
        print("-" * 50)
        if detected_sizes:
            print(f"检测成功的尺寸: {len(detected_sizes)} 个")
            print("推荐使用第一个检测成功的尺寸进行标定")
            return detected_sizes[0]
        else:
            print("未检测到任何棋盘格，请检查:")
            print("1. 棋盘格是否在摄像头视野内")
            print("2. 光照是否充足")
            print("3. 棋盘格是否平整清晰")
            return None

def main():
    parser = argparse.ArgumentParser(description='棋盘格检测工具')
    parser.add_argument('--camera', type=int, default=0, help='摄像头ID')
    parser.add_argument('--live', action='store_true', help='实时检测模式')
    parser.add_argument('--test', action='store_true', help='测试所有尺寸')
    
    args = parser.parse_args()
    
    detector = ChessboardDetector()
    
    if args.live:
        detected_size = detector.detect_chessboard_live(args.camera)
        if detected_size:
            print(f"\n最终检测结果: {detected_size[0]}x{detected_size[1]} 内角点")
            print(f"对应方格数: {detected_size[0]+1}x{detected_size[1]+1}")
            print("\n请记录这个参数用于标定!")
    elif args.test:
        detected_size = detector.test_chessboard_sizes(args.camera)
        if detected_size:
            print(f"\n推荐参数: {detected_size[0]}x{detected_size[1]} 内角点")
    else:
        print("请选择模式:")
        print("--live  : 实时检测模式")
        print("--test  : 测试所有尺寸模式")

if __name__ == "__main__":
    main()
