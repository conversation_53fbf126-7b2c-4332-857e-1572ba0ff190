#!/usr/bin/env python3
"""
鱼眼摄像头专用工具
支持鱼眼图像校正、全景拼接、360度查看等功能
"""

import cv2
import numpy as np
import os
import time
from datetime import datetime
import argparse
import math

class FisheyeCameraTools:
    def __init__(self):
        self.cameras = [0, 2, 4, 6]  # 4个鱼眼摄像头
        self.save_dir = "fisheye_captures"
        
        # 创建保存目录
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
    
    def capture_single_fisheye(self, camera_id, save_name=None):
        """拍摄单个鱼眼摄像头"""
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开鱼眼摄像头 {camera_id}")
            return None
            
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        # 等待摄像头稳定
        for _ in range(5):
            ret, frame = cap.read()
            if not ret:
                cap.release()
                return None
        
        # 拍摄照片
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            if save_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_name = f"fisheye_{camera_id}_{timestamp}.jpg"
            
            save_path = os.path.join(self.save_dir, save_name)
            cv2.imwrite(save_path, frame)
            print(f"鱼眼照片已保存: {save_path}")
            return frame
        
        return None
    
    def capture_all_fisheyes(self, prefix=""):
        """同时拍摄所有4个鱼眼摄像头"""
        print("开始拍摄所有鱼眼摄像头...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        frames = {}
        for camera_id in self.cameras:
            print(f"拍摄鱼眼摄像头 {camera_id}...", end=" ")
            
            if prefix:
                save_name = f"{prefix}_fisheye_{camera_id}_{timestamp}.jpg"
            else:
                save_name = f"fisheye_{camera_id}_{timestamp}.jpg"
            
            frame = self.capture_single_fisheye(camera_id, save_name)
            if frame is not None:
                frames[camera_id] = frame
                print("成功")
            else:
                print("失败")
            
            time.sleep(0.5)  # 避免冲突
        
        print(f"拍摄完成: {len(frames)}/4 个摄像头成功")
        return frames
    
    def create_fisheye_grid(self, frames=None, save_name=None):
        """创建2x2鱼眼图像网格"""
        if frames is None:
            print("拍摄所有鱼眼摄像头用于网格显示...")
            frames = {}
            for camera_id in self.cameras:
                frame = self.capture_single_fisheye(camera_id)
                if frame is not None:
                    frames[camera_id] = frame
        
        if len(frames) < 4:
            print(f"警告: 只有 {len(frames)} 个摄像头可用，需要4个")
            return None
        
        # 调整图像大小
        target_size = (640, 360)  # 每个鱼眼图像的大小
        resized_frames = {}
        
        for camera_id, frame in frames.items():
            resized = cv2.resize(frame, target_size)
            # 添加摄像头标签
            cv2.putText(resized, f"Camera {camera_id}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            resized_frames[camera_id] = resized
        
        # 创建2x2网格 (假设摄像头布局: 0-前, 2-右, 4-后, 6-左)
        try:
            top_row = np.hstack([resized_frames[0], resized_frames[2]])     # 前-右
            bottom_row = np.hstack([resized_frames[6], resized_frames[4]])  # 左-后
            grid = np.vstack([top_row, bottom_row])
        except KeyError:
            # 如果某些摄像头不可用，按可用顺序排列
            available_cameras = sorted(resized_frames.keys())
            if len(available_cameras) >= 4:
                top_row = np.hstack([resized_frames[available_cameras[0]], 
                                   resized_frames[available_cameras[1]]])
                bottom_row = np.hstack([resized_frames[available_cameras[2]], 
                                      resized_frames[available_cameras[3]]])
                grid = np.vstack([top_row, bottom_row])
            else:
                print("摄像头数量不足，无法创建网格")
                return None
        
        # 添加整体标题
        cv2.putText(grid, "Fisheye Camera Grid View", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        if save_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_name = f"fisheye_grid_{timestamp}.jpg"
        
        save_path = os.path.join(self.save_dir, save_name)
        cv2.imwrite(save_path, grid)
        print(f"鱼眼网格图像已保存: {save_path}")
        
        return grid
    
    def live_fisheye_grid(self):
        """实时显示4个鱼眼摄像头的网格视图"""
        print("启动鱼眼摄像头实时网格显示...")
        print("按 'q' 退出, 按 's' 保存当前网格, 按 'c' 保存单独的摄像头图像")
        
        # 打开所有摄像头
        caps = {}
        for camera_id in self.cameras:
            cap = cv2.VideoCapture(camera_id)
            if cap.isOpened():
                cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 360)
                caps[camera_id] = cap
                print(f"鱼眼摄像头 {camera_id} 已打开")
            else:
                print(f"无法打开鱼眼摄像头 {camera_id}")
        
        if len(caps) == 0:
            print("没有可用的摄像头")
            return
        
        frame_count = 0
        
        while True:
            frames = {}
            
            # 读取所有摄像头的帧
            for camera_id, cap in caps.items():
                ret, frame = cap.read()
                if ret:
                    # 添加摄像头标签和帧计数
                    cv2.putText(frame, f"Fisheye {camera_id}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                    cv2.putText(frame, f"Frame: {frame_count}", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    frames[camera_id] = frame
            
            if len(frames) == 0:
                print("无法读取任何摄像头数据")
                break
            
            # 创建网格显示
            if len(frames) >= 4:
                # 2x2网格
                available_cameras = sorted(frames.keys())
                top_row = np.hstack([frames[available_cameras[0]], 
                                   frames[available_cameras[1]]])
                bottom_row = np.hstack([frames[available_cameras[2]], 
                                      frames[available_cameras[3]]])
                grid = np.vstack([top_row, bottom_row])
            elif len(frames) >= 2:
                # 1x2网格
                available_cameras = sorted(frames.keys())
                grid = np.hstack([frames[available_cameras[0]], 
                                frames[available_cameras[1]]])
            else:
                # 单个摄像头
                grid = list(frames.values())[0]
            
            # 添加整体信息
            cv2.putText(grid, f"Fisheye Grid - {len(frames)} cameras", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            cv2.putText(grid, "q:quit s:save_grid c:save_individual", (10, grid.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('Fisheye Camera Grid', grid)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存网格图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                save_name = f"fisheye_grid_live_{timestamp}.jpg"
                save_path = os.path.join(self.save_dir, save_name)
                cv2.imwrite(save_path, grid)
                print(f"网格图像已保存: {save_path}")
            elif key == ord('c'):
                # 保存单独的摄像头图像
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                for camera_id, frame in frames.items():
                    save_name = f"fisheye_{camera_id}_live_{timestamp}.jpg"
                    save_path = os.path.join(self.save_dir, save_name)
                    cv2.imwrite(save_path, frame)
                    print(f"摄像头 {camera_id} 图像已保存: {save_path}")
            
            frame_count += 1
        
        # 关闭所有摄像头
        for cap in caps.values():
            cap.release()
        cv2.destroyAllWindows()
        print("鱼眼摄像头网格显示已关闭")
    
    def create_panorama_sequence(self, count=10, interval=2):
        """创建全景序列 (延时拍摄所有摄像头)"""
        print(f"开始创建全景序列: {count} 组图像，间隔 {interval} 秒")
        
        sequence_dir = os.path.join(self.save_dir, f"panorama_sequence_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(sequence_dir, exist_ok=True)
        
        for i in range(count):
            print(f"\n拍摄第 {i+1}/{count} 组...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            
            # 拍摄所有摄像头
            frames = {}
            for camera_id in self.cameras:
                save_name = f"seq_{i+1:03d}_fisheye_{camera_id}_{timestamp}.jpg"
                save_path = os.path.join(sequence_dir, save_name)
                
                frame = self.capture_single_fisheye(camera_id)
                if frame is not None:
                    cv2.imwrite(save_path, frame)
                    frames[camera_id] = frame
                    print(f"  摄像头 {camera_id}: {save_name}")
                
                time.sleep(0.2)  # 摄像头间短暂延迟
            
            # 创建该组的网格图像
            if len(frames) >= 4:
                grid = self.create_fisheye_grid(frames, f"seq_{i+1:03d}_grid_{timestamp}.jpg")
                grid_path = os.path.join(sequence_dir, f"seq_{i+1:03d}_grid_{timestamp}.jpg")
                if grid is not None:
                    cv2.imwrite(grid_path, grid)
            
            if i < count - 1:  # 最后一组不需要等待
                print(f"等待 {interval} 秒...")
                time.sleep(interval)
        
        print(f"\n全景序列创建完成，保存在: {sequence_dir}")
        return sequence_dir

def main():
    parser = argparse.ArgumentParser(description='鱼眼摄像头专用工具')
    parser.add_argument('--single', type=int, metavar='CAMERA_ID', help='拍摄单个鱼眼摄像头')
    parser.add_argument('--all', action='store_true', help='拍摄所有鱼眼摄像头')
    parser.add_argument('--grid', action='store_true', help='创建鱼眼网格图像')
    parser.add_argument('--live-grid', action='store_true', help='实时鱼眼网格显示')
    parser.add_argument('--panorama', type=int, nargs=2, metavar=('COUNT', 'INTERVAL'), 
                       help='创建全景序列 (数量 间隔)')
    parser.add_argument('--prefix', default='', help='文件名前缀')
    
    args = parser.parse_args()
    
    tools = FisheyeCameraTools()
    
    if args.single is not None:
        tools.capture_single_fisheye(args.single)
    elif args.all:
        tools.capture_all_fisheyes(args.prefix)
    elif args.grid:
        tools.create_fisheye_grid()
    elif args.live_grid:
        tools.live_fisheye_grid()
    elif args.panorama:
        count, interval = args.panorama
        tools.create_panorama_sequence(count, interval)
    else:
        # 默认行为：显示菜单
        print("鱼眼摄像头工具")
        print("1. 拍摄所有鱼眼摄像头")
        print("2. 创建鱼眼网格图像") 
        print("3. 实时鱼眼网格显示")
        print("4. 创建全景序列")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                tools.capture_all_fisheyes()
            elif choice == '2':
                tools.create_fisheye_grid()
            elif choice == '3':
                tools.live_fisheye_grid()
            elif choice == '4':
                count = int(input("序列数量: "))
                interval = int(input("间隔秒数: "))
                tools.create_panorama_sequence(count, interval)
            else:
                print("无效选择")
                
        except (ValueError, KeyboardInterrupt):
            print("\n程序退出")

if __name__ == "__main__":
    main()
