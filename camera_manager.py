#!/usr/bin/env python3
"""
实时摄像头管理工具
支持随时打开/关闭摄像头，保存图像，便于识别摄像头位置
"""

import cv2
import numpy as np
import threading
import time
import os
from datetime import datetime
import json

class CameraManager:
    def __init__(self):
        self.cameras = [0, 2, 4, 6]  # 4个鱼眼摄像头
        self.active_cameras = {}  # 存储活跃的摄像头对象
        self.camera_threads = {}  # 存储摄像头线程
        self.running = {}  # 控制线程运行状态
        self.save_dir = "camera_identification"
        
        # 创建保存目录
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
        
        # 加载摄像头位置映射（如果存在）
        self.position_map = self.load_position_map()
    
    def load_position_map(self):
        """加载摄像头位置映射"""
        try:
            if os.path.exists("camera_position_map.json"):
                with open("camera_position_map.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {}
    
    def save_position_map(self):
        """保存摄像头位置映射"""
        try:
            with open("camera_position_map.json", "w") as f:
                json.dump(self.position_map, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存位置映射失败: {e}")
    
    def test_camera_access(self, camera_id):
        """测试摄像头是否可以访问"""
        try:
            cap = cv2.VideoCapture(camera_id)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
            return False
        except:
            return False
    
    def camera_thread(self, camera_id):
        """摄像头线程函数"""
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"❌ 无法打开摄像头 {camera_id}")
            return
        
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        print(f"✅ 摄像头 {camera_id} 已启动")
        
        frame_count = 0
        last_save_time = 0
        
        while self.running.get(camera_id, False):
            ret, frame = cap.read()
            if not ret:
                print(f"❌ 摄像头 {camera_id} 读取失败")
                break
            
            frame_count += 1
            current_time = time.time()
            
            # 获取摄像头位置信息
            position = self.position_map.get(str(camera_id), "未知位置")
            
            # 添加摄像头信息
            cv2.putText(frame, f"Camera {camera_id}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
            cv2.putText(frame, f"Position: {position}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, f"Frame: {frame_count}", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, f"Time: {datetime.now().strftime('%H:%M:%S')}", (10, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # 添加控制提示
            cv2.putText(frame, "Controls:", (10, frame.shape[0] - 80), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, "S: Save image", (10, frame.shape[0] - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(frame, "P: Set position", (10, frame.shape[0] - 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(frame, "Q: Close camera", (10, frame.shape[0] - 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 添加彩色边框以便区分
            colors = {0: (0, 255, 0), 2: (255, 0, 0), 4: (0, 0, 255), 6: (255, 255, 0)}
            color = colors.get(camera_id, (255, 255, 255))
            cv2.rectangle(frame, (5, 5), (frame.shape[1]-5, frame.shape[0]-5), color, 3)
            
            # 显示图像
            cv2.imshow(f'Camera {camera_id} - {position}', frame)
            
            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print(f"🔴 用户关闭摄像头 {camera_id}")
                break
            elif key == ord('s'):
                # 保存图像
                if current_time - last_save_time > 1:  # 防止重复保存
                    self.save_image(camera_id, frame)
                    last_save_time = current_time
            elif key == ord('p'):
                # 设置位置
                self.set_camera_position(camera_id)
        
        cap.release()
        cv2.destroyWindow(f'Camera {camera_id} - {position}')
        print(f"🔴 摄像头 {camera_id} 已关闭")
        
        # 清理
        if camera_id in self.active_cameras:
            del self.active_cameras[camera_id]
        if camera_id in self.camera_threads:
            del self.camera_threads[camera_id]
        if camera_id in self.running:
            del self.running[camera_id]
    
    def save_image(self, camera_id, frame):
        """保存图像"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        position = self.position_map.get(str(camera_id), "unknown")
        filename = f"cam{camera_id}_{position}_{timestamp}.jpg"
        filepath = os.path.join(self.save_dir, filename)
        
        cv2.imwrite(filepath, frame)
        print(f"📸 保存图像: {filename}")
    
    def set_camera_position(self, camera_id):
        """设置摄像头位置"""
        print(f"\n设置摄像头 {camera_id} 的位置:")
        print("1. 前方")
        print("2. 右侧") 
        print("3. 后方")
        print("4. 左侧")
        print("5. 其他")
        
        try:
            choice = input("选择位置 (1-5): ").strip()
            positions = {"1": "前方", "2": "右侧", "3": "后方", "4": "左侧", "5": "其他"}
            
            if choice in positions:
                self.position_map[str(camera_id)] = positions[choice]
                self.save_position_map()
                print(f"✅ 摄像头 {camera_id} 位置设置为: {positions[choice]}")
            else:
                print("❌ 无效选择")
        except:
            print("❌ 输入错误")
    
    def start_camera(self, camera_id):
        """启动摄像头"""
        if camera_id in self.active_cameras:
            print(f"⚠️ 摄像头 {camera_id} 已经在运行")
            return False
        
        if not self.test_camera_access(camera_id):
            print(f"❌ 摄像头 {camera_id} 不可访问")
            return False
        
        # 启动摄像头线程
        self.running[camera_id] = True
        thread = threading.Thread(target=self.camera_thread, args=(camera_id,))
        thread.daemon = True
        thread.start()
        
        self.active_cameras[camera_id] = True
        self.camera_threads[camera_id] = thread
        
        return True
    
    def stop_camera(self, camera_id):
        """停止摄像头"""
        if camera_id not in self.active_cameras:
            print(f"⚠️ 摄像头 {camera_id} 未在运行")
            return False
        
        # 停止线程
        self.running[camera_id] = False
        
        # 等待线程结束
        if camera_id in self.camera_threads:
            self.camera_threads[camera_id].join(timeout=2)
        
        print(f"🔴 摄像头 {camera_id} 已停止")
        return True
    
    def stop_all_cameras(self):
        """停止所有摄像头"""
        camera_ids = list(self.active_cameras.keys())
        for camera_id in camera_ids:
            self.stop_camera(camera_id)
        
        # 关闭所有OpenCV窗口
        cv2.destroyAllWindows()
        print("🔴 所有摄像头已停止")
    
    def show_status(self):
        """显示摄像头状态"""
        print(f"\n📊 摄像头状态:")
        print("-" * 50)
        
        for camera_id in self.cameras:
            if camera_id in self.active_cameras:
                position = self.position_map.get(str(camera_id), "未知位置")
                print(f"🟢 摄像头 {camera_id}: 运行中 - {position}")
            else:
                if self.test_camera_access(camera_id):
                    position = self.position_map.get(str(camera_id), "未知位置")
                    print(f"⚪ 摄像头 {camera_id}: 可用 - {position}")
                else:
                    print(f"🔴 摄像头 {camera_id}: 不可访问")
        
        print("-" * 50)
        print(f"活跃摄像头数量: {len(self.active_cameras)}")
        print(f"保存目录: {self.save_dir}")
    
    def interactive_menu(self):
        """交互式菜单"""
        print("🎥 实时摄像头管理工具")
        print("=" * 50)
        
        while True:
            print(f"\n📋 主菜单:")
            print("1. 启动摄像头")
            print("2. 停止摄像头") 
            print("3. 查看状态")
            print("4. 启动所有摄像头")
            print("5. 停止所有摄像头")
            print("6. 设置摄像头位置")
            print("7. 查看位置映射")
            print("0. 退出")
            
            try:
                choice = input("\n请选择 (0-7): ").strip()
                
                if choice == '0':
                    self.stop_all_cameras()
                    print("👋 再见!")
                    break
                    
                elif choice == '1':
                    self.show_status()
                    camera_id = int(input("输入要启动的摄像头ID (0,2,4,6): "))
                    if camera_id in self.cameras:
                        self.start_camera(camera_id)
                    else:
                        print("❌ 无效的摄像头ID")
                        
                elif choice == '2':
                    self.show_status()
                    camera_id = int(input("输入要停止的摄像头ID (0,2,4,6): "))
                    if camera_id in self.cameras:
                        self.stop_camera(camera_id)
                    else:
                        print("❌ 无效的摄像头ID")
                        
                elif choice == '3':
                    self.show_status()
                    
                elif choice == '4':
                    print("🚀 启动所有摄像头...")
                    for camera_id in self.cameras:
                        if camera_id not in self.active_cameras:
                            self.start_camera(camera_id)
                            time.sleep(0.5)  # 避免同时启动冲突
                    
                elif choice == '5':
                    self.stop_all_cameras()
                    
                elif choice == '6':
                    self.show_status()
                    camera_id = int(input("输入要设置位置的摄像头ID (0,2,4,6): "))
                    if camera_id in self.cameras:
                        self.set_camera_position(camera_id)
                    else:
                        print("❌ 无效的摄像头ID")
                        
                elif choice == '7':
                    print(f"\n📍 摄像头位置映射:")
                    print("-" * 30)
                    for camera_id in self.cameras:
                        position = self.position_map.get(str(camera_id), "未设置")
                        print(f"摄像头 {camera_id}: {position}")
                    print("-" * 30)
                    
                else:
                    print("❌ 无效选择")
                    
            except (ValueError, KeyboardInterrupt):
                print("\n🔴 程序中断")
                self.stop_all_cameras()
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='实时摄像头管理工具')
    parser.add_argument('--start', type=int, help='启动指定摄像头')
    parser.add_argument('--start-all', action='store_true', help='启动所有摄像头')
    parser.add_argument('--status', action='store_true', help='显示状态')
    
    args = parser.parse_args()
    
    manager = CameraManager()
    
    if args.start is not None:
        if args.start in manager.cameras:
            manager.start_camera(args.start)
            try:
                while args.start in manager.active_cameras:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_camera(args.start)
        else:
            print(f"❌ 无效的摄像头ID: {args.start}")
            
    elif args.start_all:
        print("🚀 启动所有摄像头...")
        for camera_id in manager.cameras:
            manager.start_camera(camera_id)
            time.sleep(0.5)
        
        try:
            while manager.active_cameras:
                time.sleep(1)
        except KeyboardInterrupt:
            manager.stop_all_cameras()
            
    elif args.status:
        manager.show_status()
        
    else:
        # 交互式菜单
        manager.interactive_menu()

if __name__ == "__main__":
    main()
