#!/usr/bin/env python3
"""
简单的Camera查看和保存Demo
支持实时查看和保存图片
"""

import cv2
import os
import time
from datetime import datetime
import argparse

class CameraDemo:
    def __init__(self):
        self.cameras = {}
        self.save_dir = "camera_captures"
        
        # 创建保存目录
        if not os.path.exists(self.save_dir):
            os.makedirs(self.save_dir)
            
    def detect_cameras(self):
        """检测可用的camera设备"""
        print("正在检测camera设备...")
        available_cameras = []
        
        # 检测/dev/video*设备
        for i in range(8):  # 检测video0到video7
            video_path = f"/dev/video{i}"
            if os.path.exists(video_path):
                # 尝试打开camera
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    # 获取camera信息
                    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    fps = int(cap.get(cv2.CAP_PROP_FPS))
                    
                    available_cameras.append({
                        'id': i,
                        'device': video_path,
                        'resolution': f"{width}x{height}",
                        'fps': fps
                    })
                    print(f"发现Camera {i}: {video_path} ({width}x{height} @ {fps}fps)")
                cap.release()
                
        return available_cameras
    
    def capture_single_photo(self, camera_id, save_name=None):
        """拍摄单张照片"""
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开Camera {camera_id}")
            return False
            
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        # 等待camera稳定
        for _ in range(5):
            ret, frame = cap.read()
            if not ret:
                print(f"Camera {camera_id} 读取失败")
                cap.release()
                return False
        
        # 拍摄照片
        ret, frame = cap.read()
        if ret:
            if save_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                save_name = f"camera_{camera_id}_{timestamp}.jpg"
            
            save_path = os.path.join(self.save_dir, save_name)
            cv2.imwrite(save_path, frame)
            print(f"照片已保存: {save_path}")
            
            # 显示图片信息
            height, width = frame.shape[:2]
            print(f"图片尺寸: {width}x{height}")
            
        cap.release()
        return ret
    
    def capture_multiple_photos(self, camera_id, count=5, interval=1):
        """拍摄多张照片"""
        print(f"开始从Camera {camera_id}拍摄{count}张照片，间隔{interval}秒...")
        
        for i in range(count):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            save_name = f"camera_{camera_id}_batch_{i+1:02d}_{timestamp}.jpg"
            
            if self.capture_single_photo(camera_id, save_name):
                print(f"拍摄完成 {i+1}/{count}")
            else:
                print(f"拍摄失败 {i+1}/{count}")
                
            if i < count - 1:  # 最后一张不需要等待
                time.sleep(interval)
    
    def live_view(self, camera_id):
        """实时查看camera"""
        print(f"开始实时查看Camera {camera_id}...")
        print("按 'q' 退出, 按 's' 保存当前帧, 按 'c' 连续保存")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开Camera {camera_id}")
            return
            
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        
        continuous_save = False
        save_counter = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("读取帧失败")
                break
                
            # 在图像上显示信息
            cv2.putText(frame, f"Camera {camera_id}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, "q:退出 s:保存 c:连续保存", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            if continuous_save:
                cv2.putText(frame, "连续保存中...", (10, 110), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            cv2.imshow(f'Camera {camera_id} Live View', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存单张
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                save_name = f"camera_{camera_id}_live_{timestamp}.jpg"
                save_path = os.path.join(self.save_dir, save_name)
                cv2.imwrite(save_path, frame)
                print(f"保存照片: {save_path}")
            elif key == ord('c'):
                # 切换连续保存模式
                continuous_save = not continuous_save
                print(f"连续保存模式: {'开启' if continuous_save else '关闭'}")
                save_counter = 0
                
            # 连续保存
            if continuous_save:
                save_counter += 1
                if save_counter % 30 == 0:  # 每30帧保存一次（约1秒）
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                    save_name = f"camera_{camera_id}_continuous_{timestamp}.jpg"
                    save_path = os.path.join(self.save_dir, save_name)
                    cv2.imwrite(save_path, frame)
                    print(f"连续保存: {save_path}")
        
        cap.release()
        cv2.destroyAllWindows()
    
    def test_all_cameras(self):
        """测试所有camera并保存一张照片"""
        cameras = self.detect_cameras()
        if not cameras:
            print("未发现可用的camera设备")
            return
            
        print(f"\n开始测试所有{len(cameras)}个camera...")
        for cam_info in cameras:
            camera_id = cam_info['id']
            print(f"\n测试Camera {camera_id}...")
            self.capture_single_photo(camera_id)

def main():
    parser = argparse.ArgumentParser(description='Camera Demo - 简单的camera查看和保存工具')
    parser.add_argument('--detect', action='store_true', help='检测可用的camera设备')
    parser.add_argument('--live', type=int, metavar='CAMERA_ID', help='实时查看指定camera')
    parser.add_argument('--photo', type=int, metavar='CAMERA_ID', help='从指定camera拍摄一张照片')
    parser.add_argument('--batch', type=int, nargs=2, metavar=('CAMERA_ID', 'COUNT'), 
                       help='从指定camera拍摄多张照片')
    parser.add_argument('--test-all', action='store_true', help='测试所有camera')
    
    args = parser.parse_args()
    
    demo = CameraDemo()
    
    if args.detect:
        cameras = demo.detect_cameras()
        if cameras:
            print(f"\n发现 {len(cameras)} 个可用camera:")
            for cam in cameras:
                print(f"  Camera {cam['id']}: {cam['device']} - {cam['resolution']} @ {cam['fps']}fps")
        else:
            print("未发现可用的camera设备")
            
    elif args.live is not None:
        demo.live_view(args.live)
        
    elif args.photo is not None:
        demo.capture_single_photo(args.photo)
        
    elif args.batch:
        camera_id, count = args.batch
        demo.capture_multiple_photos(camera_id, count)
        
    elif args.test_all:
        demo.test_all_cameras()
        
    else:
        # 默认行为：检测camera并提供交互式菜单
        cameras = demo.detect_cameras()
        if not cameras:
            print("未发现可用的camera设备")
            return
            
        print(f"\n发现 {len(cameras)} 个可用camera:")
        for cam in cameras:
            print(f"  {cam['id']}: {cam['device']} - {cam['resolution']} @ {cam['fps']}fps")
            
        print("\n请选择操作:")
        print("1. 实时查看camera")
        print("2. 拍摄单张照片")
        print("3. 拍摄多张照片")
        print("4. 测试所有camera")
        
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                camera_id = int(input("请输入camera ID: "))
                demo.live_view(camera_id)
            elif choice == '2':
                camera_id = int(input("请输入camera ID: "))
                demo.capture_single_photo(camera_id)
            elif choice == '3':
                camera_id = int(input("请输入camera ID: "))
                count = int(input("请输入拍摄数量: "))
                demo.capture_multiple_photos(camera_id, count)
            elif choice == '4':
                demo.test_all_cameras()
            else:
                print("无效选择")
                
        except (ValueError, KeyboardInterrupt):
            print("\n程序退出")

if __name__ == "__main__":
    main()
