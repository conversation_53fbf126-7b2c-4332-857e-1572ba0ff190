# 鱼眼摄像头标定与校正完整指南

## 🔬 鱼眼校正原理详解

### 1. 鱼眼镜头的成像原理

**鱼眼镜头特点:**
- **超广角视野**: 通常具有180°或更大的视野角度
- **非线性投影**: 使用等距投影、等立体角投影等特殊投影方式
- **径向畸变**: 图像边缘严重弯曲，直线变成曲线

**鱼眼投影模型:**
```
r = f * θ  (等距投影)
其中: r = 像点到图像中心的距离
     f = 焦距
     θ = 入射光线与光轴的夹角
```

### 2. OpenCV鱼眼校正算法

OpenCV使用**Kannala-Brandt模型**进行鱼眼校正:

**畸变模型:**
```
θ_d = θ * (1 + k1*θ² + k2*θ⁴ + k3*θ⁶ + k4*θ⁸)
```

**相机内参矩阵:**
```
K = [fx  0  cx]
    [0  fy  cy]
    [0   0   1]
```

**畸变系数:**
```
D = [k1, k2, k3, k4]
```

### 3. 为什么需要标定？

#### ❌ 估算参数的问题:
- **不准确**: 每个镜头的畸变特性都不同
- **通用性差**: 同型号镜头也有个体差异
- **效果不佳**: 校正后仍有明显畸变残留

#### ✅ 真实标定的优势:
- **精确**: 基于实际图像数据计算
- **个性化**: 针对特定镜头的精确参数
- **可验证**: 通过重投影误差评估精度

## 🎯 完整标定流程

### 步骤1: 准备标定板

**推荐标定板:**
- **棋盘格标定板**: 9x6内角点 (推荐)
- **方格大小**: 25mm (可调整)
- **材质**: 平整的硬质材料
- **打印质量**: 高分辨率，边缘清晰

### 步骤2: 拍摄标定图像

```bash
# 拍摄摄像头0的标定图像
python3 fisheye_calibration.py --camera 0 --capture --num-images 25
```

**拍摄要求:**
- **数量**: 至少20张，推荐25-30张
- **角度**: 从不同角度拍摄标定板
- **距离**: 近距离、中距离、远距离各拍摄几张
- **位置**: 标定板在图像的不同位置
- **覆盖**: 尽量覆盖整个图像区域

**拍摄技巧:**
- 标定板要完全在视野内
- 避免运动模糊
- 确保标定板平整
- 光照均匀，避免反光

### 步骤3: 执行标定

```bash
# 对摄像头0进行标定
python3 fisheye_calibration.py --camera 0 --calibrate
```

**标定过程:**
1. **角点检测**: 在每张图像中检测棋盘格角点
2. **数据准备**: 建立3D-2D点对应关系
3. **参数优化**: 使用非线性优化算法求解相机参数
4. **误差评估**: 计算重投影误差(RMS)

**标定质量评估:**
- **RMS < 0.5**: 优秀
- **RMS < 1.0**: 良好  
- **RMS < 2.0**: 可接受
- **RMS > 2.0**: 需要重新标定

### 步骤4: 测试标定结果

```bash
# 实时测试标定效果
python3 fisheye_calibration.py --camera 0 --test
```

## 🛠️ 高级校正工具使用

### 1. 基于真实标定数据的校正

```bash
# 实时高级校正
python3 fisheye_correction_advanced.py --camera 0 --live

# 处理单张图像
python3 fisheye_correction_advanced.py --camera 0 --image path/to/image.jpg --types undistort balanced cropped comparison

# 批量处理
python3 fisheye_correction_advanced.py --camera 0 --dir fisheye_captures --types undistort balanced
```

### 2. 校正模式说明

#### **标准校正 (balance=0.6)**
- 平衡视野保留和畸变校正
- 适合大多数应用场景
- 轻微黑边，保留较多有效区域

#### **平衡校正 (balance=1.0)**
- 最大化保留原始视野
- 适合需要完整视野的应用
- 可能有较多黑边

#### **裁剪校正 (balance=0.0)**
- 完全去除黑边
- 损失部分视野
- 适合需要矩形图像的应用

### 3. 实时校正控制

在实时校正模式下:
- **'1'**: 切换到标准校正
- **'2'**: 切换到平衡校正  
- **'3'**: 切换到裁剪校正
- **'c'**: 切换对比模式
- **'s'**: 保存当前帧
- **'q'**: 退出

## 📊 标定数据格式

标定完成后，会生成JSON格式的标定文件:

```json
{
  "camera_id": 0,
  "rms_error": 0.234,
  "camera_matrix": [
    [896.123, 0.0, 640.456],
    [0.0, 897.789, 360.123], 
    [0.0, 0.0, 1.0]
  ],
  "distortion_coefficients": [
    [-0.234], [0.123], [-0.012], [0.003]
  ],
  "image_size": [1280, 720],
  "calibration_date": "2024-08-14T15:30:45",
  "num_images": 25,
  "board_size": [9, 6],
  "square_size": 25.0
}
```

## 🔧 故障排除

### 1. 角点检测失败
**原因:**
- 标定板不够清晰
- 光照不均匀
- 标定板倾斜过度
- 图像模糊

**解决方案:**
- 使用高质量标定板
- 改善光照条件
- 重新拍摄标定图像
- 调整标定板大小参数

### 2. 标定精度不高 (RMS > 2.0)
**原因:**
- 标定图像数量不足
- 标定图像质量差
- 标定板参数设置错误

**解决方案:**
- 增加标定图像数量
- 提高图像拍摄质量
- 检查标定板尺寸设置
- 重新标定

### 3. 校正效果不理想
**原因:**
- 使用了错误的标定数据
- 标定数据过期
- 摄像头硬件变化

**解决方案:**
- 确认使用正确的标定文件
- 重新进行标定
- 检查摄像头硬件状态

## 🚀 最佳实践

### 1. 标定频率
- **新摄像头**: 必须标定
- **定期检查**: 每3-6个月重新标定
- **硬件变化**: 更换镜头后重新标定

### 2. 标定环境
- **稳定光照**: 避免强光和阴影
- **稳定支架**: 使用三脚架固定摄像头
- **平整标定板**: 确保标定板完全平整

### 3. 数据管理
- **版本控制**: 保留历史标定数据
- **备份**: 定期备份标定文件
- **文档**: 记录标定条件和参数

## 📈 应用场景

### 1. 机器人导航
- 障碍物检测
- 路径规划
- SLAM建图

### 2. 监控系统
- 全景监控
- 入侵检测
- 行为分析

### 3. VR/AR应用
- 全景视频
- 虚拟现实
- 增强现实

### 4. 科学研究
- 生物观察
- 环境监测
- 实验记录

---

**总结**: 通过专业的标定流程，我们可以获得精确的鱼眼摄像头参数，实现高质量的畸变校正，充分发挥鱼眼摄像头的优势。
