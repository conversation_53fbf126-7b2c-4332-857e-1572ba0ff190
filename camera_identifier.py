#!/usr/bin/env python3
"""
摄像头识别工具
帮助识别哪个物理摄像头对应哪个设备号
"""

import cv2
import numpy as np
import time
import threading
import argparse

class CameraIdentifier:
    def __init__(self):
        self.cameras = [0, 2, 4, 6]  # 4个鱼眼摄像头
        self.active_cameras = {}
        self.running = False
    
    def test_camera_access(self, camera_id):
        """测试摄像头是否可以访问"""
        try:
            cap = cv2.VideoCapture(camera_id)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
            return False
        except:
            return False
    
    def show_single_camera(self, camera_id):
        """显示单个摄像头的实时画面"""
        print(f"显示摄像头 {camera_id} - 按 'q' 退出")
        
        cap = cv2.VideoCapture(camera_id)
        if not cap.isOpened():
            print(f"无法打开摄像头 {camera_id}")
            return False
        
        # 设置分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        frame_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print(f"摄像头 {camera_id} 读取失败")
                break
            
            frame_count += 1
            
            # 添加摄像头信息
            cv2.putText(frame, f"Camera {camera_id}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
            cv2.putText(frame, f"Frame: {frame_count}", (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            cv2.putText(frame, "Press 'q' to quit", (10, 110), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 添加明显的边框
            cv2.rectangle(frame, (5, 5), (frame.shape[1]-5, frame.shape[0]-5), (0, 255, 0), 5)
            
            cv2.imshow(f'Camera {camera_id} Identification', frame)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()
        return True
    
    def show_all_cameras_grid(self):
        """在网格中同时显示所有摄像头"""
        print("同时显示所有摄像头 - 按 'q' 退出")
        print("观察每个窗口，记录物理位置对应的摄像头ID")
        
        # 打开所有可用摄像头
        caps = {}
        for camera_id in self.cameras:
            if self.test_camera_access(camera_id):
                cap = cv2.VideoCapture(camera_id)
                if cap.isOpened():
                    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 320)
                    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 240)
                    caps[camera_id] = cap
                    print(f"✅ 摄像头 {camera_id} 已打开")
                else:
                    print(f"❌ 摄像头 {camera_id} 打开失败")
            else:
                print(f"❌ 摄像头 {camera_id} 不可访问")
        
        if not caps:
            print("没有可用的摄像头")
            return
        
        frame_count = 0
        
        while True:
            frames = {}
            
            # 读取所有摄像头的帧
            for camera_id, cap in caps.items():
                ret, frame = cap.read()
                if ret:
                    # 调整尺寸
                    frame = cv2.resize(frame, (320, 240))
                    
                    # 添加摄像头标识
                    cv2.putText(frame, f"CAM {camera_id}", (10, 30), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                    cv2.putText(frame, f"Frame: {frame_count}", (10, 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    
                    # 添加彩色边框以便区分
                    colors = {0: (0, 255, 0), 2: (255, 0, 0), 4: (0, 0, 255), 6: (255, 255, 0)}
                    color = colors.get(camera_id, (255, 255, 255))
                    cv2.rectangle(frame, (2, 2), (318, 238), color, 4)
                    
                    frames[camera_id] = frame
            
            if not frames:
                print("无法读取任何摄像头数据")
                break
            
            # 创建网格显示
            if len(frames) >= 4:
                # 2x2网格
                camera_ids = sorted(frames.keys())
                top_row = np.hstack([frames[camera_ids[0]], frames[camera_ids[1]]])
                bottom_row = np.hstack([frames[camera_ids[2]], frames[camera_ids[3]]])
                grid = np.vstack([top_row, bottom_row])
            elif len(frames) >= 2:
                # 1x2网格
                camera_ids = sorted(frames.keys())
                grid = np.hstack([frames[camera_ids[0]], frames[camera_ids[1]]])
                if len(frames) >= 3:
                    third_frame = frames[camera_ids[2]]
                    # 调整第三个摄像头的尺寸以匹配
                    third_frame = cv2.resize(third_frame, (grid.shape[1], 240))
                    grid = np.vstack([grid, third_frame])
            else:
                # 单个摄像头
                grid = list(frames.values())[0]
            
            # 添加整体信息
            cv2.putText(grid, f"Camera Grid - {len(frames)} cameras active", (10, 25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(grid, "Observe physical camera positions", (10, grid.shape[0] - 40), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(grid, "Press 'q' to quit", (10, grid.shape[0] - 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            cv2.imshow('All Cameras Grid', grid)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            
            frame_count += 1
        
        # 关闭所有摄像头
        for cap in caps.values():
            cap.release()
        cv2.destroyAllWindows()
    
    def sequential_camera_test(self):
        """逐个测试摄像头，便于识别"""
        print("逐个测试摄像头，便于识别物理位置")
        print("每个摄像头会显示5秒，观察哪个物理摄像头亮起")
        
        for camera_id in self.cameras:
            print(f"\n🎯 测试摄像头 {camera_id}...")
            print("观察哪个物理摄像头显示图像")
            
            if not self.test_camera_access(camera_id):
                print(f"❌ 摄像头 {camera_id} 不可访问")
                continue
            
            cap = cv2.VideoCapture(camera_id)
            if not cap.isOpened():
                print(f"❌ 摄像头 {camera_id} 打开失败")
                continue
            
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            start_time = time.time()
            frame_count = 0
            
            print(f"📹 摄像头 {camera_id} 显示中... (5秒)")
            
            while time.time() - start_time < 5:  # 显示5秒
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                remaining = 5 - int(time.time() - start_time)
                
                # 添加大号标识
                cv2.putText(frame, f"CAMERA {camera_id}", (50, 100), 
                           cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 255, 0), 5)
                cv2.putText(frame, f"Remaining: {remaining}s", (50, 150), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 3)
                cv2.putText(frame, "Observe which physical camera is active", (50, 200), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                
                # 添加闪烁效果
                if frame_count % 20 < 10:  # 每秒闪烁一次
                    cv2.rectangle(frame, (10, 10), (frame.shape[1]-10, frame.shape[0]-10), 
                                 (0, 255, 0), 10)
                
                cv2.imshow(f'Testing Camera {camera_id}', frame)
                
                if cv2.waitKey(1) & 0xFF == ord('q'):
                    break
            
            cap.release()
            cv2.destroyAllWindows()
            
            # 询问用户识别结果
            position = input(f"摄像头 {camera_id} 的物理位置 (前/后/左/右/其他): ").strip()
            if position:
                print(f"✅ 记录: 摄像头 {camera_id} = {position}")
            
            if camera_id != self.cameras[-1]:  # 不是最后一个
                choice = input("继续测试下一个摄像头? (y/n): ").strip().lower()
                if choice == 'n':
                    break
    
    def create_camera_map(self):
        """创建摄像头映射"""
        print("创建摄像头物理位置映射")
        print("请根据实际观察结果填写每个摄像头的物理位置")
        
        camera_map = {}
        positions = ["前方", "右侧", "后方", "左侧", "其他"]
        
        for camera_id in self.cameras:
            if self.test_camera_access(camera_id):
                print(f"\n摄像头 {camera_id} 的物理位置:")
                for i, pos in enumerate(positions):
                    print(f"  {i+1}. {pos}")
                
                try:
                    choice = int(input("选择位置 (1-5): ")) - 1
                    if 0 <= choice < len(positions):
                        camera_map[camera_id] = positions[choice]
                        print(f"✅ 摄像头 {camera_id} -> {positions[choice]}")
                    else:
                        camera_map[camera_id] = "未知"
                except:
                    camera_map[camera_id] = "未知"
            else:
                print(f"❌ 摄像头 {camera_id} 不可访问")
        
        # 保存映射
        print(f"\n📋 摄像头位置映射:")
        print("-" * 30)
        for camera_id, position in camera_map.items():
            print(f"摄像头 {camera_id}: {position}")
        print("-" * 30)
        
        # 保存到文件
        import json
        with open("camera_position_map.json", "w") as f:
            json.dump(camera_map, f, indent=2, ensure_ascii=False)
        print("✅ 映射已保存到 camera_position_map.json")
        
        return camera_map

def main():
    parser = argparse.ArgumentParser(description='摄像头识别工具')
    parser.add_argument('--single', type=int, help='显示单个摄像头')
    parser.add_argument('--grid', action='store_true', help='网格显示所有摄像头')
    parser.add_argument('--sequential', action='store_true', help='逐个测试摄像头')
    parser.add_argument('--map', action='store_true', help='创建摄像头位置映射')
    
    args = parser.parse_args()
    
    identifier = CameraIdentifier()
    
    if args.single is not None:
        identifier.show_single_camera(args.single)
    elif args.grid:
        identifier.show_all_cameras_grid()
    elif args.sequential:
        identifier.sequential_camera_test()
    elif args.map:
        identifier.create_camera_map()
    else:
        # 交互式菜单
        print("🎯 摄像头识别工具")
        print("1. 逐个测试摄像头 (推荐)")
        print("2. 网格显示所有摄像头")
        print("3. 显示单个摄像头")
        print("4. 创建位置映射")
        
        try:
            choice = input("\n请选择 (1-4): ").strip()
            
            if choice == '1':
                identifier.sequential_camera_test()
            elif choice == '2':
                identifier.show_all_cameras_grid()
            elif choice == '3':
                camera_id = int(input("输入摄像头ID (0,2,4,6): "))
                identifier.show_single_camera(camera_id)
            elif choice == '4':
                identifier.create_camera_map()
            else:
                print("无效选择")
                
        except (ValueError, KeyboardInterrupt):
            print("\n程序退出")

if __name__ == "__main__":
    main()
