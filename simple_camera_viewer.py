#!/usr/bin/env python3
"""
简单的Camera实时查看器
"""

import cv2
import sys
import argparse

def view_camera(camera_id):
    """实时查看指定camera"""
    print(f"正在打开Camera {camera_id}...")
    print("按 'q' 退出, 按 's' 保存当前帧")
    
    cap = cv2.VideoCapture(camera_id)
    if not cap.isOpened():
        print(f"错误: 无法打开Camera {camera_id}")
        return False
        
    # 设置分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    
    # 获取实际分辨率
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    
    print(f"Camera {camera_id} 已打开: {width}x{height} @ {fps}fps")
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            print("读取帧失败")
            break
            
        frame_count += 1
        
        # 在图像上显示信息
        cv2.putText(frame, f"Camera {camera_id}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, f"Frame: {frame_count}", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, f"Size: {width}x{height}", (10, 110), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, "Press 'q' to quit, 's' to save", (10, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        cv2.imshow(f'Camera {camera_id} Live View', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s'):
            # 保存当前帧
            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{camera_id}_frame_{timestamp}.jpg"
            cv2.imwrite(filename, frame)
            print(f"保存照片: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    print(f"Camera {camera_id} 已关闭")
    return True

def main():
    parser = argparse.ArgumentParser(description='简单的Camera实时查看器')
    parser.add_argument('camera_id', type=int, help='Camera ID (0, 2, 4, 6)')
    
    args = parser.parse_args()
    
    # 检查camera_id是否有效
    valid_cameras = [0, 2, 4, 6]
    if args.camera_id not in valid_cameras:
        print(f"警告: Camera {args.camera_id} 可能不可用")
        print(f"建议使用: {valid_cameras}")
    
    view_camera(args.camera_id)

if __name__ == "__main__":
    main()
