#!/usr/bin/env python3
"""
标定图像筛选工具
分析和筛选高质量的标定图像，解决病态矩阵问题
"""

import cv2
import numpy as np
import os
import glob
import json
import shutil
from datetime import datetime

class CalibrationImageFilter:
    def __init__(self):
        self.calibration_dir = "fisheye_calibration"
        self.filtered_dir = "fisheye_calibration_filtered"
        self.backup_dir = "fisheye_calibration_backup"
        
        # 创建目录
        for dir_name in [self.filtered_dir, self.backup_dir]:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
        
        # 棋盘格参数
        self.possible_board_sizes = [
            (8, 11), (11, 8), (9, 6), (8, 6), (7, 5)
        ]
        self.board_size = (8, 11)
        
    def analyze_image_quality(self, image_path):
        """分析单张图像的质量"""
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 尝试检测棋盘格
        corners = None
        detected_size = None
        
        for board_size in self.possible_board_sizes:
            ret, corners_temp = cv2.findChessboardCorners(gray, board_size, None)
            if ret:
                # 精确化角点
                criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
                corners = cv2.cornerSubPix(gray, corners_temp, (11, 11), (-1, -1), criteria)
                detected_size = board_size
                break
        
        if corners is None:
            return None
        
        # 计算质量指标
        quality_metrics = {}
        
        # 1. 角点分布均匀性
        corners_flat = corners.reshape(-1, 2)
        center = np.mean(corners_flat, axis=0)
        distances = np.linalg.norm(corners_flat - center, axis=1)
        quality_metrics['distribution_std'] = np.std(distances)
        
        # 2. 角点到图像边缘的距离
        h, w = gray.shape
        min_edge_dist = min(
            np.min(corners_flat[:, 0]),  # 左边距
            np.min(corners_flat[:, 1]),  # 上边距
            w - np.max(corners_flat[:, 0]),  # 右边距
            h - np.max(corners_flat[:, 1])   # 下边距
        )
        quality_metrics['edge_distance'] = min_edge_dist
        
        # 3. 棋盘格覆盖面积
        min_x, min_y = np.min(corners_flat, axis=0)
        max_x, max_y = np.max(corners_flat, axis=0)
        coverage_area = (max_x - min_x) * (max_y - min_y)
        total_area = w * h
        quality_metrics['coverage_ratio'] = coverage_area / total_area
        
        # 4. 角点检测精度（亚像素精度的标准差）
        # 重新检测角点来评估精度
        ret2, corners2 = cv2.findChessboardCorners(gray, detected_size, None)
        if ret2:
            diff = np.linalg.norm(corners.reshape(-1, 2) - corners2.reshape(-1, 2), axis=1)
            quality_metrics['detection_precision'] = np.mean(diff)
        else:
            quality_metrics['detection_precision'] = 999
        
        # 5. 图像清晰度（拉普拉斯方差）
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        quality_metrics['sharpness'] = laplacian_var
        
        # 6. 棋盘格倾斜角度
        # 计算棋盘格的主要方向
        if len(corners_flat) >= 4:
            # 使用PCA计算主方向
            coords_centered = corners_flat - center
            cov_matrix = np.cov(coords_centered.T)
            eigenvals, eigenvecs = np.linalg.eig(cov_matrix)
            main_direction = eigenvecs[:, np.argmax(eigenvals)]
            angle = np.arctan2(main_direction[1], main_direction[0]) * 180 / np.pi
            quality_metrics['tilt_angle'] = abs(angle)
        else:
            quality_metrics['tilt_angle'] = 0
        
        return {
            'path': image_path,
            'detected_size': detected_size,
            'corners': corners,
            'metrics': quality_metrics
        }
    
    def filter_images(self, camera_id, max_images=30, min_images=20):
        """筛选高质量的标定图像"""
        print(f"🔍 分析摄像头 {camera_id} 的标定图像...")
        
        # 查找所有标定图像
        pattern = os.path.join(self.calibration_dir, f"calib_cam{camera_id}_*.jpg")
        image_files = glob.glob(pattern)
        
        if not image_files:
            print(f"❌ 未找到摄像头 {camera_id} 的标定图像")
            return False
        
        print(f"📁 找到 {len(image_files)} 张图像，开始质量分析...")
        
        # 分析所有图像
        image_analyses = []
        for img_file in image_files:
            analysis = self.analyze_image_quality(img_file)
            if analysis:
                image_analyses.append(analysis)
                print(f"✅ {os.path.basename(img_file)}: 质量分析完成")
            else:
                print(f"❌ {os.path.basename(img_file)}: 无法检测棋盘格")
        
        if len(image_analyses) < min_images:
            print(f"❌ 可用图像不足 ({len(image_analyses)} < {min_images})")
            return False
        
        print(f"📊 成功分析 {len(image_analyses)} 张图像")
        
        # 计算质量分数
        self.calculate_quality_scores(image_analyses)
        
        # 按质量分数排序
        image_analyses.sort(key=lambda x: x['quality_score'], reverse=True)
        
        # 选择最佳图像，确保分布均匀
        selected_images = self.select_diverse_images(image_analyses, max_images)
        
        print(f"🎯 选择了 {len(selected_images)} 张高质量图像")
        
        # 备份原始图像
        self.backup_original_images(camera_id)
        
        # 复制筛选后的图像
        self.copy_filtered_images(selected_images, camera_id)
        
        # 显示筛选结果
        self.show_filtering_results(image_analyses, selected_images)
        
        return True
    
    def calculate_quality_scores(self, image_analyses):
        """计算每张图像的质量分数"""
        # 收集所有指标
        all_metrics = [img['metrics'] for img in image_analyses]
        
        # 计算指标的统计信息用于归一化
        metrics_stats = {}
        for key in all_metrics[0].keys():
            values = [m[key] for m in all_metrics]
            metrics_stats[key] = {
                'mean': np.mean(values),
                'std': np.std(values),
                'min': np.min(values),
                'max': np.max(values)
            }
        
        # 为每张图像计算质量分数
        for img_analysis in image_analyses:
            metrics = img_analysis['metrics']
            score = 0
            
            # 1. 覆盖率分数 (越大越好)
            coverage_score = metrics['coverage_ratio'] * 100
            score += coverage_score * 0.3
            
            # 2. 边缘距离分数 (适中最好)
            edge_dist = metrics['edge_distance']
            if edge_dist > 50:
                edge_score = 100
            elif edge_dist > 20:
                edge_score = 80
            else:
                edge_score = 50
            score += edge_score * 0.2
            
            # 3. 清晰度分数 (越大越好)
            sharpness = metrics['sharpness']
            if sharpness > 500:
                sharpness_score = 100
            elif sharpness > 200:
                sharpness_score = 80
            else:
                sharpness_score = 50
            score += sharpness_score * 0.2
            
            # 4. 倾斜角度分数 (适度倾斜最好)
            tilt = metrics['tilt_angle']
            if 10 <= tilt <= 45:
                tilt_score = 100
            elif 5 <= tilt <= 60:
                tilt_score = 80
            else:
                tilt_score = 60
            score += tilt_score * 0.2
            
            # 5. 检测精度分数 (越小越好)
            precision = metrics['detection_precision']
            if precision < 0.5:
                precision_score = 100
            elif precision < 1.0:
                precision_score = 80
            else:
                precision_score = 50
            score += precision_score * 0.1
            
            img_analysis['quality_score'] = score
    
    def select_diverse_images(self, image_analyses, max_images):
        """选择多样化的图像，避免相似图像过多"""
        if len(image_analyses) <= max_images:
            return image_analyses
        
        selected = []
        remaining = image_analyses.copy()
        
        # 首先选择质量最高的图像
        selected.append(remaining.pop(0))
        
        # 然后选择与已选图像差异最大的图像
        while len(selected) < max_images and remaining:
            best_candidate = None
            max_diversity = -1
            
            for candidate in remaining:
                # 计算与已选图像的多样性
                diversity = self.calculate_diversity(candidate, selected)
                if diversity > max_diversity:
                    max_diversity = diversity
                    best_candidate = candidate
            
            if best_candidate:
                selected.append(best_candidate)
                remaining.remove(best_candidate)
            else:
                break
        
        return selected
    
    def calculate_diversity(self, candidate, selected_images):
        """计算候选图像与已选图像的多样性"""
        if not selected_images:
            return 1.0
        
        candidate_metrics = candidate['metrics']
        diversities = []
        
        for selected in selected_images:
            selected_metrics = selected['metrics']
            
            # 计算关键指标的差异
            coverage_diff = abs(candidate_metrics['coverage_ratio'] - selected_metrics['coverage_ratio'])
            tilt_diff = abs(candidate_metrics['tilt_angle'] - selected_metrics['tilt_angle'])
            edge_diff = abs(candidate_metrics['edge_distance'] - selected_metrics['edge_distance'])
            
            # 综合多样性分数
            diversity = coverage_diff * 0.4 + tilt_diff * 0.4 + edge_diff * 0.2
            diversities.append(diversity)
        
        # 返回最小多样性（确保与所有已选图像都有足够差异）
        return min(diversities)
    
    def backup_original_images(self, camera_id):
        """备份原始图像"""
        pattern = os.path.join(self.calibration_dir, f"calib_cam{camera_id}_*.jpg")
        image_files = glob.glob(pattern)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_subdir = os.path.join(self.backup_dir, f"cam{camera_id}_{timestamp}")
        os.makedirs(backup_subdir, exist_ok=True)
        
        for img_file in image_files:
            shutil.copy2(img_file, backup_subdir)
        
        print(f"💾 原始图像已备份到: {backup_subdir}")
    
    def copy_filtered_images(self, selected_images, camera_id):
        """复制筛选后的图像到原目录"""
        # 清空原目录中该摄像头的图像
        pattern = os.path.join(self.calibration_dir, f"calib_cam{camera_id}_*.jpg")
        old_files = glob.glob(pattern)
        for old_file in old_files:
            os.remove(old_file)
        
        # 复制筛选后的图像
        for i, img_analysis in enumerate(selected_images):
            old_path = img_analysis['path']
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            new_filename = f"calib_cam{camera_id}_{i:02d}_{timestamp}.jpg"
            new_path = os.path.join(self.calibration_dir, new_filename)
            
            shutil.copy2(old_path, new_path)
        
        print(f"📁 筛选后的图像已保存到: {self.calibration_dir}")
    
    def show_filtering_results(self, all_images, selected_images):
        """显示筛选结果"""
        print(f"\n📊 筛选结果统计:")
        print(f"  原始图像数量: {len(all_images)}")
        print(f"  筛选后数量: {len(selected_images)}")
        print(f"  筛选比例: {len(selected_images)/len(all_images)*100:.1f}%")
        
        print(f"\n🏆 选中图像的质量分数:")
        for i, img in enumerate(selected_images[:10]):  # 显示前10个
            score = img['quality_score']
            filename = os.path.basename(img['path'])
            print(f"  {i+1:2d}. {filename}: {score:.1f}分")
        
        if len(selected_images) > 10:
            print(f"  ... 还有 {len(selected_images)-10} 张图像")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='标定图像筛选工具')
    parser.add_argument('--camera', type=int, required=True, help='摄像头ID')
    parser.add_argument('--max-images', type=int, default=25, help='最大保留图像数')
    parser.add_argument('--min-images', type=int, default=15, help='最小需要图像数')
    
    args = parser.parse_args()
    
    filter_tool = CalibrationImageFilter()
    
    if filter_tool.filter_images(args.camera, args.max_images, args.min_images):
        print(f"\n🎉 图像筛选完成！")
        print(f"现在可以重新运行标定:")
        print(f"python3 fisheye_calibration_interactive.py --camera {args.camera} --calibrate-only")
    else:
        print(f"\n❌ 图像筛选失败")

if __name__ == "__main__":
    main()
